using Application.Core;
using Application.Extensions;
using Database.Repositories.Simulation;
using Database.Repositories.User;
using Model.Core.Search.Pagination;
using Model.Simulation.Search.Request;
using Model.Simulation.Search.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<
    Model.Core.Search.Pagination.PaginationResponse>;

namespace Application.Simulation.Search;

public sealed class SearchSimulationUseCase : ISearchSimulationUseCase
{
    private readonly IUserRepository _userRepository;
    private readonly ISimulationRepository _simulationRepository;
    private readonly SearchSimulationRequestValidator _requestValidator = new();

    public SearchSimulationUseCase(
        IUserRepository userRepository,
        ISimulationRepository simulationRepository)
    {
        _userRepository = userRepository;
        _simulationRepository = simulationRepository;
    }

    public async Task<UseCaseResponse<PaginationResponse>> Execute(
        SearchSimulationRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null, "000", "Request cannot be empty.");
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var result = await _simulationRepository.SearchAsync(request);

            return result.CurrentItemsCount > 0
                ? Ok(result)
                : NoContent();
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}
