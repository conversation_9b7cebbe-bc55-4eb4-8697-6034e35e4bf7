using Application.Core;
using Application.Services.BlobStorage;
using Application.Simulation.GetById;
using Database.Repositories.Simulation;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;
using Moq;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.Simulation
{
    [Trait("GetSimulationByIdUseCase", "Performance")]
    public class GetByIdPerformanceTests
    {
        private readonly Mock<ISimulationRepository> _simulationRepository;
        private readonly Mock<IBlobStorageService> _blobStorageService;
        private readonly Mock<IOptions<BlobStorageOptions>> _blobStorageOptions;
        private readonly IGetSimulationByIdUseCase _useCase;

        public GetByIdPerformanceTests()
        {
            _simulationRepository = new Mock<ISimulationRepository>();
            _blobStorageService = new Mock<IBlobStorageService>();
            _blobStorageOptions = new Mock<IOptions<BlobStorageOptions>>();
            
            _blobStorageOptions.Setup(x => x.Value)
                .Returns(new BlobStorageOptions { ClientsContainer = "test-container" });

            _useCase = new GetSimulationByIdUseCase(
                _simulationRepository.Object,
                _blobStorageService.Object,
                _blobStorageOptions.Object);
        }

        [Fact(DisplayName = "RefactoredGetByIdAsync_CompletesWithinReasonableTime")]
        public async Task RefactoredGetByIdAsync_CompletesWithinReasonableTime()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid()
            };

            var simulationResponse = CreateComplexSimulationResponse(request.Id);

            _simulationRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
                .ReturnsAsync(simulationResponse);

            // Act & Assert - Measure execution time
            var stopwatch = Stopwatch.StartNew();
            var result = await _useCase.Execute(request);
            stopwatch.Stop();

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.OK);
            result.Result.Should().NotBeNull();
            
            // Performance assertion - should complete within reasonable time
            // This is a baseline test - the refactored version should be faster
            stopwatch.ElapsedMilliseconds.Should().BeLessThan(1000, "GetByIdAsync should complete within 1 second for complex data");
        }

        [Fact(DisplayName = "RefactoredGetByIdAsync_HandlesComplexDataStructure")]
        public async Task RefactoredGetByIdAsync_HandlesComplexDataStructure()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid()
            };

            var simulationResponse = CreateComplexSimulationResponse(request.Id);

            _simulationRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
                .ReturnsAsync(simulationResponse);

            // Act
            var result = await _useCase.Execute(request);

            // Assert - Verify all complex data is properly mapped
            result.Status.Should().Be(UseCaseResponseKind.OK);
            result.Result.Should().NotBeNull();
            result.Result.Id.Should().Be(request.Id);
            result.Result.Sections.Should().HaveCount(3);
            result.Result.Events.Should().HaveCount(2);
            
            // Verify sections have instruments, results, and warnings
            foreach (var section in result.Result.Sections)
            {
                section.Instruments.Should().HaveCount(2);
                section.Results.Should().HaveCount(2);
                section.Warnings.Should().HaveCount(1);
            }
        }

        private GetSimulationByIdResponse CreateComplexSimulationResponse(Guid simulationId)
        {
            return new GetSimulationByIdResponse
            {
                Id = simulationId,
                Name = "Complex Test Simulation",
                Status = Domain.Enums.SimulationStatus.Completed,
                ShouldKeep = true,
                ShouldEvaluateDrainedCondition = true,
                ShouldEvaluateUndrainedCondition = true,
                ShouldEvaluatePseudoStaticCondition = true,
                SafetyFactorTarget = 1.5,
                CreatedBy = new Model._Shared.User.User
                {
                    Id = Guid.NewGuid(),
                    FirstName = "Test",
                    Surname = "User",
                    Username = "testuser"
                },
                SeismicCoefficient = new Model._Shared.Orientation.Orientation
                {
                    Horizontal = 0.1,
                    Vertical = 0.05
                },
                Slide2Configuration = new Model._Shared.Slide2Configuration.Slide2Configuration
                {
                    CircularParameters = new Model._Shared.Slide2Configuration.CircularParameters
                    {
                        CircularSearchMethod = Domain.Enums.CircularSearchMethod.GridSearch,
                        DivisionsAlongSlope = 10,
                        CirclesPerDivision = 5,
                        CalculationMethods = new System.Collections.Generic.List<Domain.Enums.CalculationMethod>
                        {
                            Domain.Enums.CalculationMethod.BishopSimplified,
                            Domain.Enums.CalculationMethod.Spencer
                        }
                    }
                },
                Sections = CreateComplexSections(),
                Events = CreateComplexEvents()
            };
        }

        private System.Collections.Generic.List<GetSimulationByIdSection> CreateComplexSections()
        {
            var sections = new System.Collections.Generic.List<GetSimulationByIdSection>();
            
            for (int i = 0; i < 3; i++)
            {
                sections.Add(new GetSimulationByIdSection
                {
                    Id = Guid.NewGuid(),
                    SectionId = Guid.NewGuid(),
                    SectionName = $"Test Section {i + 1}",
                    MinimumDrainedDepth = 10.5 + i,
                    MinimumUndrainedDepth = 8.5 + i,
                    Instruments = CreateComplexInstruments(),
                    Results = CreateComplexResults(),
                    Warnings = CreateComplexWarnings()
                });
            }
            
            return sections;
        }

        private System.Collections.Generic.List<GetSimulationByIdInstrument> CreateComplexInstruments()
        {
            return new System.Collections.Generic.List<GetSimulationByIdInstrument>
            {
                new GetSimulationByIdInstrument
                {
                    Id = Guid.NewGuid(),
                    InstrumentId = Guid.NewGuid(),
                    InstrumentIdentifier = "INST-001",
                    InstrumentType = Domain.Enums.InstrumentType.ElectricPiezometer,
                    Quota = 100.5m
                },
                new GetSimulationByIdInstrument
                {
                    Id = Guid.NewGuid(),
                    InstrumentId = Guid.NewGuid(),
                    InstrumentIdentifier = "INST-002",
                    InstrumentType = Domain.Enums.InstrumentType.ConventionalInclinometer,
                    Quota = 95.2m
                }
            };
        }

        private System.Collections.Generic.List<GetSimulationByIdResult> CreateComplexResults()
        {
            return new System.Collections.Generic.List<GetSimulationByIdResult>
            {
                new GetSimulationByIdResult
                {
                    Id = Guid.NewGuid(),
                    CalculationMethod = Domain.Enums.CalculationMethod.BishopSimplified,
                    Value = 1.45,
                    SliFileType = Domain.Enums.SliFileType.CircularDrained
                },
                new GetSimulationByIdResult
                {
                    Id = Guid.NewGuid(),
                    CalculationMethod = Domain.Enums.CalculationMethod.Spencer,
                    Value = 1.52,
                    SliFileType = Domain.Enums.SliFileType.NonCircularDrained
                }
            };
        }

        private System.Collections.Generic.List<GetSimulationByIdWarning> CreateComplexWarnings()
        {
            return new System.Collections.Generic.List<GetSimulationByIdWarning>
            {
                new GetSimulationByIdWarning
                {
                    Id = Guid.NewGuid(),
                    Message = "Test warning message",
                    CreatedDate = DateTime.UtcNow
                }
            };
        }

        private System.Collections.Generic.List<GetSimulationByIdEvent> CreateComplexEvents()
        {
            return new System.Collections.Generic.List<GetSimulationByIdEvent>
            {
                new GetSimulationByIdEvent
                {
                    Id = Guid.NewGuid(),
                    Event = "Simulation started",
                    CreatedDate = DateTime.UtcNow.AddHours(-2)
                },
                new GetSimulationByIdEvent
                {
                    Id = Guid.NewGuid(),
                    Event = "Simulation completed",
                    CreatedDate = DateTime.UtcNow.AddHours(-1)
                }
            };
        }
    }
}
