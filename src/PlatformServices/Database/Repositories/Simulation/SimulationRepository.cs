using Dapper;
using Database.Repositories.Outbox;
using Domain.Entities;
using Domain.Enums;
using Domain.Messages.Commands.Simulation;
using Domain.ValueObjects;
using Model.Simulation.List.Request;
using Model.Simulation.List.Response;
using Model.Simulation.Search.Request;
using Model.Simulation.Search.Response;
using Model.Simulation.UpdateStatus.Request;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Model.Core.Search.Pagination;
using Model.Reading.Search.Response;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;

namespace Database.Repositories.Simulation
{
    public class SimulationRepository : ISimulationRepository
    {
        private readonly string _connectionString;

        public SimulationRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<IEnumerable<ListSimulationResponse>> ListAsync(ListSimulationRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<ListSimulationResponse>(Queries.List, new
            {
                request.DaysOfCreation,
                request.ShouldKeep
            });
        }

        public async Task UpdateStatusAsync(UpdateSimulationStatusRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpdateStatus, new { request.Id, request.Status }, transaction);

                if (!string.IsNullOrEmpty(request.Event))   
                {
                    var param = new
                    {
                        Id = Guid.NewGuid(),
                        request.Event,
                        SimulationId = request.Id
                    };

                    await connection.ExecuteAsync(Queries.InsertSimulationEvent, param, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<PaginationResponse> SearchAsync(SearchSimulationRequest request)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@Skip", request.Query.GetSkip());
            parameters.Add("@PageSize", request.Query.PageSize);
            parameters.Add("@RequestedBy", request.RequestedBy);
            
            var builder = new SqlBuilder()
                .Where("[simulation-users].[user-id] = @RequestedBy");

            if (!string.IsNullOrWhiteSpace(request.Query.Name))
            {
                parameters.Add(
                    "@Name", 
                    request.Query.Name, 
                    DbType.AnsiString, 
                    size: 255);
                
                builder = builder.Where(
                    "[simulations].[name] LIKE CONCAT('%', @Name, '%')");
            }

            if (request.Query.ClientId.HasValue)
            {
                parameters.Add("@ClientId", request.Query.ClientId);
                builder = builder
                    .Where("[client-units].[client-id] = @ClientId");
            }

            if (request.Query.ClientUnitId.HasValue)
            {
                parameters.Add("@ClientUnitId", request.Query.ClientUnitId);
                builder = builder
                    .Where("[client-units].[id] = @ClientUnitId");
            }
            
            if (request.Query.StructureId.HasValue)
            {
                parameters.Add("@StructureId", request.Query.StructureId);
                builder = builder
                    .Where("[structures].[id] = @StructureId");
            }
            
            if (request.Query.SearchIdentifier.HasValue)
            {
                parameters.Add("@SearchIdentifier", request.Query.SearchIdentifier);
                builder = builder
                    .Where("[simulations].[search-identifier] = @SearchIdentifier");
            }
            
            if (request.Body.SectionIds.Any())
            {
                parameters.Add("@SectionIds", request.Body.SectionIds);
                builder = builder
                    .Where("[sections].[id] IN @SectionIds");
            }
            
            if (!request.RequestedBySuperSupport)
            {
                parameters.Add("@RequestedUserStructures", request.RequestedUserStructures);
                builder = builder
                    .Where("[structures].[id] IN @RequestedUserStructures");
            }

            var itemsQuery = builder.AddTemplate(Queries.Search);
            var countQuery = builder.AddTemplate(Queries.Count);

            var lookup = new Dictionary<Guid, SearchSimulationResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: itemsQuery.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Simulation),
                    typeof(Domain.Entities.User),
                    typeof(Domain.Entities.SimulationSection),
                    typeof(Domain.Entities.Section),
                    typeof(Domain.Entities.SimulationResult),
                    typeof(Model._Shared.Structure.Structure)
                },
                (records) =>
                {
                    var simulationDb = records[0] as Domain.Entities.Simulation;
                    var createdBy = records[1] as Domain.Entities.User;
                    var simulationSection = records[2] as SimulationSection;
                    var section = records[3] as Domain.Entities.Section;
                    var structure = records[5] as Model._Shared.Structure.Structure;

                    if (!lookup.TryGetValue(simulationDb.Id, out var simulation))
                    {
                        var simulationModel = new SearchSimulationResponse
                        {
                            Id = simulationDb.Id,
                            Name = simulationDb.Name,
                            Status = simulationDb.Status,
                            ShouldKeep = simulationDb.ShouldKeep,
                            SearchIdentifier = simulationDb.SearchIdentifier,
                            CreatedBy = new()
                            {
                                Id = createdBy.Id,
                                FirstName = createdBy.FirstName,
                                Surname = createdBy.Surname,
                                Username = createdBy.Username
                            },
                            CreatedDate = simulationDb.CreatedDate,
                            NeedToDoStatisticalCalculations = simulationDb.NeedToDoStatisticalCalculations,
                            Sections = new()
                        };

                        lookup.Add(simulationDb.Id, simulationModel);

                        simulation = simulationModel;
                    }

                    if (!simulation.Sections.Any(x => x.SectionId == section.Id))
                    {
                        simulation.Sections.Add(new()
                        {
                            SectionId = section.Id,
                            SectionName = section.Name,
                            StructureId = section.StructureId,
                            Structure = structure,
                            Results = new()
                        });
                    }

                    if (records[4] is SimulationResult simulationResult)
                    {
                        var simulationSectionModel = simulation.Sections.First(x => x.SectionId == section.Id);

                        simulationSectionModel.Results.Add(new()
                        {
                            CalculationMethod = simulationResult.CalculationMethod,
                            SliFileType = simulationResult.SliFileType,
                            Value = simulationResult.Value,
                        });
                    }

                    return simulation;
                },
                splitOn: "Id,Id,Id,Id,Id",
                param: parameters);

            var items = lookup.Values.ToList();
            
            if (!items.Any())
            {
                return new PaginationResponse()
                {
                    Data = Enumerable.Empty<SearchSimulationResponse>(),
                };
            }
            
            var count = await connection.ExecuteScalarAsync<int>(
                countQuery.RawSql,
                parameters);

            return new PaginationResponse()
            {
                Data = items,
                CurrentItemsCount = items.Count(),
                TotalItemsCount = count
            };
        }

        public async Task RemoveAuthorizedUserAsync(Guid simulationId, Guid user)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteAsync(Queries.DeleteSimulationUser, new { SimulationId = simulationId, UserId = user });
        }

        public async Task AddAuthorizedUsersAsync(Guid simulationId, List<Guid> users)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            var param = users.Select(user => new
            {
                SimulationId = simulationId,
                UserId = user
            });

            await connection.ExecuteAsync(Queries.InsertSimulationUsers, param);
        }

        public async Task AddAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Name,
                    simulation.Status,
                    simulation.ShouldEvaluateDrainedCondition,
                    simulation.ShouldEvaluateUndrainedCondition,
                    simulation.ShouldEvaluatePseudoStaticCondition,
                    simulation.SafetyFactorTarget,
                    SeismicCoefficientHorizontal = simulation.SeismicCoefficient?.Horizontal,
                    SeismicCoefficientVertical = simulation.SeismicCoefficient?.Vertical,
                    simulation.Slide2Configuration.CircularParameters?.CircularSearchMethod,
                    CircularDivisionsAlongSlope = simulation.Slide2Configuration.CircularParameters?.DivisionsAlongSlope,
                    simulation.Slide2Configuration.CircularParameters?.CirclesPerDivision,
                    CircularNumberOfIterations = simulation.Slide2Configuration.CircularParameters?.NumberOfIterations,
                    CircularDivisionsNextIteration = simulation.Slide2Configuration.CircularParameters?.DivisionsNextIteration,
                    simulation.Slide2Configuration.CircularParameters?.RadiusIncrement,
                    CircularNumberOfSurfaces = simulation.Slide2Configuration.CircularParameters?.NumberOfSurfaces,
                    simulation.Slide2Configuration.NonCircularParameters?.NonCircularSearchMethod,
                    NonCircularDivisionsAlongSlope = simulation.Slide2Configuration.NonCircularParameters?.DivisionsAlongSlope,
                    simulation.Slide2Configuration.NonCircularParameters?.SurfacesPerDivision,
                    NonCircularNumberOfIterations = simulation.Slide2Configuration.NonCircularParameters?.NumberOfIterations,
                    NonCircularDivisionsNextIteration = simulation.Slide2Configuration.NonCircularParameters?.DivisionsNextIteration,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfVerticesAlongSurface,
                    NonCircularNumberOfSurfaces = simulation.Slide2Configuration.NonCircularParameters?.NumberOfSurfaces,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfNests,
                    simulation.Slide2Configuration.NonCircularParameters?.MaximumIterations,
                    simulation.Slide2Configuration.NonCircularParameters?.InitialNumberOfSurfaceVertices,
                    simulation.Slide2Configuration.NonCircularParameters?.InitialNumberOfIterations,
                    simulation.Slide2Configuration.NonCircularParameters?.MaximumNumberOfSteps,
                    NumberOfFactorsSafetyCompared = simulation.Slide2Configuration.NonCircularParameters?.NumberOfFactorsSafetyComparedBeforeStopping,
                    simulation.Slide2Configuration.NonCircularParameters?.ToleranceForStoppingCriterion,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfParticles,
                    simulation.WaterTableConfiguration,
                    simulation.ReadingStatisticalMeasure,
                    simulation.WaterTableVariation,
                    simulation.StartDate,
                    simulation.EndDate,
                    simulation.UpstreamLinimetricRulerStatisticalMeasure,
                    simulation.UpstreamLinimetricRulerQuota,
                    simulation.DownstreamLinimetricRulerStatisticalMeasure,
                    simulation.DownstreamLinimetricRulerQuota,
                    simulation.IgnoreDamagedInstruments,
                    simulation.NeedToDoStatisticalCalculations,
                    CreatedBy = simulation.CreatedBy.Id,
                    UpstreamLinimetricRulerId = simulation.UpstreamLinimetricRuler?.Id,
                    DownstreamLinimetricRulerId = simulation.DownstreamLinimetricRuler?.Id
                };

                await connection
                   .ExecuteAsync(Queries.Insert, param, transaction);

                var simulationUsersParam = simulation.AuthorizedUsers.Select(user => new
                {
                    SimulationId = simulation.Id,
                    UserId = user.Id
                });

                await connection
                    .ExecuteAsync(Queries.InsertSimulationUsers, simulationUsersParam, transaction);

                if (simulation.Slide2Configuration.CircularParameters?.CalculationMethods != null)
                {
                    var circularCalculationMethodsParam = simulation.Slide2Configuration.CircularParameters.CalculationMethods.Select(method => new
                    {
                        SimulationId = simulation.Id,
                        CalculationMethod = method
                    });

                    await connection
                        .ExecuteAsync(Queries.InsertCircularCalculationMethod, circularCalculationMethodsParam, transaction);
                }

                if (simulation.Slide2Configuration.NonCircularParameters?.CalculationMethods != null)
                {
                    var nonCircularCalculationMethodsParam = simulation.Slide2Configuration.NonCircularParameters.CalculationMethods.Select(method => new
                    {
                        SimulationId = simulation.Id,
                        CalculationMethod = method
                    });

                    await connection
                        .ExecuteAsync(Queries.InsertNonCircularCalculationMethod, nonCircularCalculationMethodsParam, transaction);
                }

                var simulationSectionParam = simulation.Sections.Select(simulationSection => new
                {
                    simulationSection.Id,
                    SectionReviewId = simulationSection.SectionReview.Id,
                    SimulationId = simulation.Id,
                    SectionId = simulationSection.Section.Id,
                    ConstructionStageId = simulationSection.ConstructionStage?.Id,
                    simulationSection.MinimumDrainedDepth,
                    simulationSection.MinimumUndrainedDepth,
                    simulationSection.MinimumPseudoStaticDepth,
                    simulationSection.BeachLengthStatisticalMeasure,
                    simulationSection.BeachLength,
                });

                await connection
                    .ExecuteAsync(Queries.InsertSimulationSection, simulationSectionParam, transaction);

                var simulationSectionInstruments = simulation.Sections.SelectMany(section => section.Instruments.Select(simulationSectionInstrument => new
                {
                    simulationSectionInstrument.Id,
                    SimulationSectionId = section.Id,
                    InstrumentId = simulationSectionInstrument.Instrument.Id,
                    MeasurementId = simulationSectionInstrument.Measurement?.Id,
                    simulationSectionInstrument.Quota,
                    simulationSectionInstrument.DryType,
                    simulationSectionInstrument.Dry
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationSectionInstrument, simulationSectionInstruments, transaction);

                foreach (var @event in simulation.Events)
                {
                    var eventParam = new
                    {
                        @event.Id,
                        @event.Event,
                        SimulationId = simulation.Id,
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertSimulationEvent, eventParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                await connection.ExecuteAsync(Queries.DeleteWarnings, new { SimulationId = id }, transaction);
                await connection.ExecuteAsync(Queries.Delete, new { Id = id }, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<GetSimulationByIdResponse> GetByIdAsync(GetSimulationByIdRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<Domain.Entities.Simulation> GetAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Simulation>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetById,
                param: new { Id = id },
                types: new[]
                {
                    typeof(Domain.Entities.Simulation),
                    typeof(Orientation),
                    typeof(CircularParameters),
                    typeof(NonCircularParameters),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                    typeof(Domain.Entities.User),
                    typeof(SimulationSection),
                    typeof(Domain.Entities.Section),
                    typeof(SimulationInstrument),
                    typeof(Domain.Entities.Instrument),
                    typeof(Measurement),
                    typeof(SimulationResult),
                    typeof(File),
                    typeof(File),
                    typeof(File),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Instrument),
                    typeof(SectionReview),
                    typeof(SimulationEvent),
                    typeof(ConstructionStage),
                    typeof(SimulationWarning),
                },
                splitOn: "Horizontal,CircularSearchMethod,NonCircularSearchMethod,CalculationMethods,CalculationMethods,Id,Id,Id,Id,Id,Id,Id,Name,Name,Name,Name,Id,Id,Id,Id,Id,Id,Id",
                map: (records) =>
                {
                    var simulation = records[0] as Domain.Entities.Simulation;
                    var circularParameters = records[2] as CircularParameters;
                    var nonCircularParameters = records[3] as NonCircularParameters;
                    var circularCalculationMethod = (CalculationMethod?)records[4];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[5];
                    var createdBy = records[6] as Domain.Entities.User;
                    var section = records[8] as Domain.Entities.Section;
                    var instrument = records[10] as Domain.Entities.Instrument;
                    var measurement = records[11] as Measurement;
                    var sliFile = records[13] as File;
                    var sltmFile = records[14] as File;
                    var dxfFile = records[15] as File;
                    var pngFile = records[16] as File;
                    var upstreamLinimetricRuler = records[18] as Domain.Entities.Instrument;
                    var downstreamLinimetricRuler = records[19] as Domain.Entities.Instrument;
                    var sectionReview = records[20] as SectionReview;

                    if (!lookup.TryGetValue(simulation.Id, out var simulationModel))
                    {
                        simulationModel = simulation;
                        simulationModel.CreatedBy = new()
                        {
                            Id = createdBy.Id,
                            FirstName = createdBy.FirstName,
                            Surname = createdBy.Surname,
                            Username = createdBy.Username
                        };
                        simulationModel.Slide2Configuration.CircularParameters = circularParameters;
                        simulationModel.Slide2Configuration.NonCircularParameters = nonCircularParameters;
                        simulationModel.UpstreamLinimetricRuler = upstreamLinimetricRuler;
                        simulationModel.DownstreamLinimetricRuler = downstreamLinimetricRuler;

                        lookup.Add(simulation.Id, simulationModel);
                    }

                    if (records[1] is Orientation orientation)
                    {
                        simulationModel.SeismicCoefficient = new()
                        {
                            Horizontal = orientation.Horizontal,
                            Vertical = orientation.Vertical
                        };
                    }

                    if (circularCalculationMethod != null && simulationModel.Slide2Configuration.CircularParameters != null && !simulationModel.Slide2Configuration.CircularParameters.CalculationMethods.Any(x => x == circularCalculationMethod))
                    {
                        simulationModel.Slide2Configuration.CircularParameters.CalculationMethods.Add(circularCalculationMethod.Value);
                    }

                    if (nonCircularCalculationMethod != null && simulationModel.Slide2Configuration.NonCircularParameters != null && !simulationModel.Slide2Configuration.NonCircularParameters.CalculationMethods.Any(x => x == nonCircularCalculationMethod))
                    {
                        simulationModel.Slide2Configuration.NonCircularParameters.CalculationMethods.Add(nonCircularCalculationMethod.Value);
                    }

                    if (records[7] is SimulationSection simulationSection)
                    {
                        var sectionModel = new SimulationSection();

                        if (!simulationModel.Sections.Any(x => x.Id == simulationSection.Id))
                        {
                            sectionModel = new SimulationSection
                            {
                                Id = simulationSection.Id,
                                Section = section,
                                SectionReview = sectionReview,
                                MinimumDrainedDepth = simulationSection.MinimumDrainedDepth,
                                MinimumUndrainedDepth = simulationSection.MinimumUndrainedDepth,
                                MinimumPseudoStaticDepth = simulationSection.MinimumPseudoStaticDepth,
                                BeachLengthStatisticalMeasure = simulationSection.BeachLengthStatisticalMeasure,
                                IgnoredInstruments = simulationSection.IgnoredInstruments,
                                BeachLength = simulationSection.BeachLength,
                                Instruments = new()
                            };

                            if (records[22] is ConstructionStage constructionStage)
                            {
                                sectionModel.ConstructionStage = constructionStage; 
                            }

                            simulationModel.Sections.Add(sectionModel);
                        }
                        else
                        {
                            sectionModel = simulationModel.Sections.First(x => x.Id == simulationSection.Id);
                        }

                        if (records[23] is SimulationWarning simulationWarning) 
                        {
                            var warningSection = simulationModel.Sections.FirstOrDefault(x => x.Id == simulationSection.Id);

                            if (warningSection != null && !warningSection.Warnings.Any(x => x.Id == simulationWarning.Id))
                            {
                                warningSection.Warnings.Add(new()
                                {
                                    Id = simulationWarning.Id,
                                    Message = simulationWarning.Message,
                                    CreatedDate = simulationWarning.CreatedDate
                                });
                            }
                        }   

                        if (records[9] is SimulationInstrument simulationInstrument && !sectionModel.Instruments.Any(x => x.Id == simulationInstrument.Id))
                        {
                            sectionModel.Instruments.Add(new()
                            {
                                Id = simulationInstrument.Id,
                                Instrument = instrument,
                                Measurement = measurement,
                                Quota = simulationInstrument.Quota,
                                DryType = simulationInstrument.DryType,
                                Dry = simulationInstrument.Dry
                            });
                        }

                        if (records[12] is SimulationResult simulationResult && !sectionModel.Results.Any(x => x.Id == simulationResult.Id))
                        {
                            sectionModel.Results.Add(new()
                            {
                                Id = simulationResult.Id,
                                CalculationMethod = simulationResult.CalculationMethod,
                                SliFileType = simulationResult.SliFileType,
                                Value = simulationResult.Value,
                                DxfFile = dxfFile,
                                PngFile = pngFile,
                                SliFile = sliFile,
                                SltmFile = sltmFile
                            });
                        }
                    }

                    if (records[17] is Domain.Entities.User user && !simulationModel.AuthorizedUsers.Any(x => x.Id == user.Id))
                    {
                        simulationModel.AuthorizedUsers.Add(new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        });
                    }

                    if (records[21] is SimulationEvent @event && !simulationModel.Events.Any(x => x.Id == @event.Id))
                    {
                        simulationModel.Events.Add(@event);
                    }

                    return simulationModel;
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpdateAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Name,
                    simulation.ShouldKeep
                };  

                await connection
                    .ExecuteAsync(Queries.UpdateNameAndKeepStatus, param, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception) 
            {
                await transaction.RollbackAsync();
                throw;
            }

        }

        public async Task PatchAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Status,
                    simulation.UpstreamLinimetricRulerQuota,
                    simulation.DownstreamLinimetricRulerQuota,
                    simulation.NeedToDoStatisticalCalculations
                };

                await connection
                    .ExecuteAsync(Queries.Update, param, transaction);

                var simulationSectionParam = simulation.Sections.Select(simulationSection => new
                {
                    simulationSection.Id,
                    simulationSection.BeachLength,
                    simulationSection.IgnoredInstruments
                });

                await connection
                    .ExecuteAsync(Queries.UpdateSimulationSection, simulationSectionParam, transaction);

                var simulationSectionInstruments = simulation.Sections.SelectMany(section => section.Instruments.Select(simulationSectionInstrument => new
                {
                    simulationSectionInstrument.Id,
                    simulationSectionInstrument.Quota
                }));

                await connection
                    .ExecuteAsync(Queries.UpdateSimulationSectionInstrument, simulationSectionInstruments, transaction);

                await connection
                    .ExecuteAsync(Queries.DeleteSimulationSectionInstrument, new { SimulationId = simulation.Id, InstrumentIds = simulation.Sections.SelectMany(x => x.Instruments).Select(x => x.Instrument.Id) }, transaction);

                var results = simulation.Sections.SelectMany(section => section.Results.Select(result => new
                {
                    result.Id,
                    SimulationSectionId = section.Id,
                    result.CalculationMethod,
                    result.SliFileType,
                    result.Value,
                    SliFileName = result.SliFile.Name,
                    SliFileUniqueName = result.SliFile.UniqueName,
                    SltmFileName = result.SltmFile.Name,
                    SltmFileUniqueName = result.SltmFile.UniqueName,
                    DxfFileName = result.DxfFile.Name,
                    DxfFileUniqueName = result.DxfFile.UniqueName,
                    PngFileName = result.PngFile.Name,
                    PngFileUniqueName = result.PngFile.UniqueName
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationResult, results, transaction);

                foreach (var @event in simulation.Events)
                {
                    var eventParam = new
                    {
                        @event.Id,
                        @event.Event,
                        SimulationId = simulation.Id
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertSimulationEvent, eventParam, transaction);
                }

                var warnings = simulation.Sections.SelectMany(section => section.Warnings.Select(warning => new
                {
                    warning.Id,
                    SimulationSectionId = section.Id,
                    warning.Message
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationWarning, warnings, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
