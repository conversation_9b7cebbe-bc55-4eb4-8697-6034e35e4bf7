using Application.Core;
using Application.Services.BlobStorage;
using Application.Simulation.GetById;
using Application.Tests.Simulation.Builders;
using Database.Repositories.Simulation;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.Simulation
{
    [Trait("GetSimulationByIdUseCase", "Execute")]
    public class GetByIdTests
    {
        private readonly Mock<ISimulationRepository> _simulationRepository;
        private readonly Mock<IBlobStorageService> _blobStorageService;
        private readonly Mock<IOptions<BlobStorageOptions>> _blobStorageOptions;
        private readonly IGetSimulationByIdUseCase _useCase;

        public GetByIdTests()
        {
            _simulationRepository = new Mock<ISimulationRepository>();
            _blobStorageService = new Mock<IBlobStorageService>();
            _blobStorageOptions = new Mock<IOptions<BlobStorageOptions>>();
            
            _blobStorageOptions.Setup(x => x.Value)
                .Returns(new BlobStorageOptions { ClientsContainer = "test-container" });

            _useCase = new GetSimulationByIdUseCase(
                _simulationRepository.Object,
                _blobStorageService.Object,
                _blobStorageOptions.Object);
        }

        [Fact(DisplayName = "WhenRequestIsNull_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsNull_ReturnsBadRequest()
        {
            // Act
            var result = await _useCase.Execute(null);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.Empty // Invalid ID
            };

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenSimulationNotFound_ReturnsNoContent")]
        public async Task Execute_WhenSimulationNotFound_ReturnsNoContent()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid()
            };

            _simulationRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
                .ReturnsAsync((GetSimulationByIdResponse)null);

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.NoContent);
        }

        [Fact(DisplayName = "WhenSimulationExists_ReturnsOk")]
        public async Task Execute_WhenSimulationExists_ReturnsOk()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid()
            };

            var simulationResponse = new GetSimulationByIdResponse
            {
                Id = request.Id,
                Name = "Test Simulation",
                Status = Domain.Enums.SimulationStatus.Completed,
                ShouldKeep = true,
                CreatedBy = new Model._Shared.User.User
                {
                    Id = Guid.NewGuid(),
                    FirstName = "Test",
                    Surname = "User",
                    Username = "testuser"
                },
                Sections = new System.Collections.Generic.List<GetSimulationByIdSection>(),
                Events = new System.Collections.Generic.List<GetSimulationByIdEvent>()
            };

            _simulationRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
                .ReturnsAsync(simulationResponse);

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.OK);
            result.Result.Should().NotBeNull();
            result.Result.Id.Should().Be(request.Id);
            result.Result.Name.Should().Be("Test Simulation");
        }

        [Fact(DisplayName = "WhenRepositoryThrowsException_ReturnsInternalServerError")]
        public async Task Execute_WhenRepositoryThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var request = new GetSimulationByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid()
            };

            _simulationRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<GetSimulationByIdRequest>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _useCase.Execute(request);

            // Assert
            result.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }
    }
}
