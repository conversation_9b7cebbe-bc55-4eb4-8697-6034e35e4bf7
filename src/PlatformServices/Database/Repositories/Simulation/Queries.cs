namespace Database.Repositories.Simulation
{
    public static class Queries
    {
        public const string Insert = @"
            INSERT INTO [simulations]
            (
                [id],
                [name],
                [status],
                [circular-search-method],
                [circular-divisions-along-slope],
                [circles-per-division],
                [circular-number-of-iterations],
                [circular-divisions-next-iteration],
                [radius-increment],
                [circular-number-of-surfaces],
                [non-circular-search-method],
                [non-circular-divisions-along-slope],
                [surfaces-per-division],
                [non-circular-number-of-iterations],
                [non-circular-divisions-next-iteration],
                [number-of-vertices-along-surface],
                [non-circular-number-of-surfaces],
                [number-of-nests],
                [maximum-iterations],
                [initial-number-of-surface-vertices],
                [initial-number-of-iterations],
                [maximum-number-of-steps],
                [number-of-factors-safety-compared],
                [tolerance-for-stopping-criterion],
                [number-of-particles],
                [should-evaluate-drained-condition],
                [should-evaluate-undrained-condition],
                [should-evaluate-pseudo-static-condition],
                [safety-factor-target],
                [water-table-configuration],
                [reading-statistical-measure],
                [water-table-variation],
                [start-date],
                [end-date],
                [upstream-linimetric-ruler-statistical-measure],
                [downstream-linimetric-ruler-statistical-measure],
                [upstream-linimetric-ruler-quota],
                [downstream-linimetric-ruler-quota],
                [seismic-coefficient-horizontal],
                [seismic-coefficient-vertical],
                [ignore-damaged-instruments],
                [need-to-do-statistical-calculations],
                [created-by],
                [upstream-linimetric-ruler-id],
                [downstream-linimetric-ruler-id]
            )
            VALUES
            (
                @Id,
                @Name,
                @Status,
                @CircularSearchMethod,
                @CircularDivisionsAlongSlope,
                @CirclesPerDivision,
                @CircularNumberOfIterations,
                @CircularDivisionsNextIteration,
                @RadiusIncrement,
                @CircularNumberOfSurfaces,
                @NonCircularSearchMethod,
                @NonCircularDivisionsAlongSlope,
                @SurfacesPerDivision,
                @NonCircularNumberOfIterations,
                @NonCircularDivisionsNextIteration,
                @NumberOfVerticesAlongSurface,
                @NonCircularNumberOfSurfaces,
                @NumberOfNests,
                @MaximumIterations,
                @InitialNumberOfSurfaceVertices,
                @InitialNumberOfIterations,
                @MaximumNumberOfSteps,
                @NumberOfFactorsSafetyCompared,
                @ToleranceForStoppingCriterion,
                @NumberOfParticles,
                @ShouldEvaluateDrainedCondition,
                @ShouldEvaluateUndrainedCondition,
                @ShouldEvaluatePseudoStaticCondition,
                @SafetyFactorTarget,
                @WaterTableConfiguration,
                @ReadingStatisticalMeasure,
                @WaterTableVariation,
                @StartDate,
                @EndDate,
                @UpstreamLinimetricRulerStatisticalMeasure,
                @DownstreamLinimetricRulerStatisticalMeasure,
                @UpstreamLinimetricRulerQuota,
                @DownstreamLinimetricRulerQuota,
                @SeismicCoefficientHorizontal,
                @SeismicCoefficientVertical,
                @IgnoreDamagedInstruments,
                @NeedToDoStatisticalCalculations,
                @CreatedBy,
                @UpstreamLinimetricRulerId,
                @DownstreamLinimetricRulerId
            )";

        public const string List = @"
            SELECT 
            [id] as Id,
            [name] as Name,
            [created-date] as CreatedDate,
            [should-keep] as ShouldKeep
            FROM [simulations]
            WHERE (@DaysOfCreation IS NULL OR [created-date] < DATEADD(DAY, -@DaysOfCreation, GETDATE()))
                AND (@ShouldKeep IS NULL OR [should-keep] = @ShouldKeep);
        ";

        public const string InsertSimulationUsers = @"
            INSERT INTO [simulation-users]
            (
                [simulation-id],
                [user-id]
            )
            VALUES
            (
                @SimulationId,
                @UserId
            )";

        public const string DeleteSimulationUser = @"
            DELETE FROM [simulation-users]
            WHERE [simulation-id] = @SimulationId
            AND [user-id] = @UserId";

        public const string InsertCircularCalculationMethod = @"
            INSERT INTO [simulation-circular-calculation-methods]
            (
                [simulation-id],
                [calculation-method]
            )
            VALUES
            (
                @SimulationId,
                @CalculationMethod
            )";

        public const string InsertNonCircularCalculationMethod = @"
            INSERT INTO [simulation-non-circular-calculation-methods]
            (
                [simulation-id],
                [calculation-method]
            )
            VALUES
            (
                @SimulationId,
                @CalculationMethod
            )";

        public const string InsertSimulationSection = @"
            INSERT INTO [simulation-sections]
            (
                [id],
                [simulation-id],
                [section-id],
                [minimum-drained-depth],
                [minimum-pseudo-static-depth],
                [minimum-undrained-depth],
                [beach-length-statistical-measure],
                [beach-length],
                [section-review-id],
                [construction-stage-id]
            )
            VALUES
            (
                @Id,
                @SimulationId,
                @SectionId,
                @MinimumDrainedDepth,
                @MinimumPseudoStaticDepth,
                @MinimumUndrainedDepth,
                @BeachLengthStatisticalMeasure,
                @BeachLength,
                @SectionReviewId,
                @ConstructionStageId
            )";

        public const string InsertSimulationWarning = @"
            INSERT INTO [simulation-warnings]
            (
                [id],
                [simulation-section-id],
                [message]
            )
            VALUES
            (
                @Id,
                @SimulationSectionId,
                @Message
            )";

        public const string InsertSimulationEvent = @"
            INSERT INTO [simulation-events]
            (
                [id],
                [simulation-id],
                [event]
            )
            VALUES
            (
                @Id,
                @SimulationId,
                @Event
            )
        ";

        public const string InsertSimulationSectionInstrument = @"
            INSERT INTO [simulation-section-instruments]
            (
                [id],
                [simulation-section-id],
                [instrument-id],
                [measurement-id],
                [quota],
                [dry-type],
                [dry]
            )
            VALUES
            (
                @Id,
                @SimulationSectionId,
                @InstrumentId,
                @MeasurementId,
                @Quota,
                @DryType,
                @Dry
            )";

        public const string UpdateStatus = @"
            UPDATE [simulations]
            SET [status] = @Status
            WHERE [id] = @Id";

        public const string Search = @"
            WITH [PagedSimulations] AS (
                SELECT
                    DISTINCT([simulations].[id]) AS [id],
                    [simulations].[created-date]
                FROM [simulations]
                    JOIN [simulation-sections] ON [simulations].[id] = [simulation-sections].[simulation-id]
                    JOIN [sections] ON [simulation-sections].[section-id] = [sections].[id]
                    JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                    JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
                    JOIN [simulation-users] ON [simulations].[id] = [simulation-users].[simulation-id]
                /**where**/
                ORDER BY [simulations].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            )
            SELECT
                [simulations].[id] AS [Id],
                [simulations].[name] AS [Name],
                [simulations].[status] AS [Status],
                [simulations].[should-keep] AS [ShouldKeep],
                [simulations].[search-identifier] AS [SearchIdentifier],
                [simulations].[created-date] AS [CreatedDate],
                [simulations].[need-to-do-statistical-calculations] AS [NeedToDoStatisticalCalculations], 
                [users].[id] AS [Id],
                [users].[first-name] AS [FirstName],
                [users].[surname] AS [Surname],
                [users].[username] AS [Username],
                [simulation-sections].[id] AS [Id],
                [sections].[id] AS [Id],
                [sections].[name] AS [Name],
                [sections].[structure-id] AS [StructureId],
                [simulation-section-results].[id] AS [Id],
                [simulation-section-results].[calculation-method] AS [CalculationMethod],
                [simulation-section-results].[sli-file-type] AS [SliFileType],
                [simulation-section-results].[value] AS [Value],
                [structures].[id] AS Id,
                [structures].[name] AS Name,
                [structures].[client-unit-id] AS ClientUnitId
            FROM [PagedSimulations]
                JOIN [simulations] ON [PagedSimulations].[Id] = [simulations].[id]
                JOIN [simulation-sections] ON [simulations].[id] = [simulation-sections].[simulation-id]
                JOIN [sections] ON [simulation-sections].[section-id] = [sections].[id]
                JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
                JOIN [simulation-users] ON [simulations].[id] = [simulation-users].[simulation-id]
                LEFT JOIN [users] ON [simulations].[created-by] = [users].[id]
                LEFT JOIN [simulation-section-results] ON [simulation-sections].[id] = [simulation-section-results].[simulation-section-id]
            ";

        public const string Count = @"
            SELECT COUNT(DISTINCT [simulations].[id])
            FROM [simulations]
                JOIN [simulation-sections] ON [simulations].[id] = [simulation-sections].[simulation-id]
                JOIN [sections] ON [simulation-sections].[section-id] = [sections].[id]
                JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
                JOIN [simulation-users] ON [simulations].[id] = [simulation-users].[simulation-id]
                LEFT JOIN [users] ON [simulations].[created-by] = [users].[id]
                LEFT JOIN [simulation-section-results] ON [simulation-sections].[id] = [simulation-section-results].[simulation-section-id]
            /**where**/";

        public const string GetById = @"
            SELECT
                [simulations].[id] AS [Id],
                [simulations].[name] AS [Name],
                [simulations].[status] AS [Status],
                [simulations].[should-keep] AS [ShouldKeep],
                [simulations].[should-evaluate-drained-condition] AS [ShouldEvaluateDrainedCondition],
                [simulations].[should-evaluate-undrained-condition] AS [ShouldEvaluateUndrainedCondition],
                [simulations].[should-evaluate-pseudo-static-condition] AS [ShouldEvaluatePseudoStaticCondition],
                [simulations].[safety-factor-target] AS [SafetyFactorTarget],
                [simulations].[water-table-configuration] AS [WaterTableConfiguration],
                [simulations].[reading-statistical-measure] AS [ReadingStatisticalMeasure],
                [simulations].[water-table-variation] AS [WaterTableVariation],
                [simulations].[start-date] AS [StartDate],
                [simulations].[end-date] AS [EndDate],
                [simulations].[upstream-linimetric-ruler-statistical-measure] AS [UpstreamLinimetricRulerStatisticalMeasure],
                [simulations].[downstream-linimetric-ruler-statistical-measure] AS [DownstreamLinimetricRulerStatisticalMeasure],
                [simulations].[upstream-linimetric-ruler-quota] AS [UpstreamLinimetricRulerQuota],
                [simulations].[downstream-linimetric-ruler-quota] AS [DownstreamLinimetricRulerQuota],
                [simulations].[ignore-damaged-instruments] AS [IgnoreDamagedInstruments],
                [simulations].[need-to-do-statistical-calculations] AS [NeedToDoStatisticalCalculations],
                [simulations].[seismic-coefficient-horizontal] AS [Horizontal],
                [simulations].[seismic-coefficient-vertical] AS [Vertical],
                [circular-search-method] AS [CircularSearchMethod],
                [circular-divisions-along-slope] AS [DivisionsAlongSlope],
                [circles-per-division] AS [CirclesPerDivision],
                [circular-number-of-iterations] AS [NumberOfIterations],
                [circular-divisions-next-iteration] AS [DivisionsNextIteration],
                [radius-increment] AS [RadiusIncrement],
                [circular-number-of-surfaces] AS [NumberOfSurfaces],
                [non-circular-search-method] AS [NonCircularSearchMethod],
                [non-circular-divisions-along-slope] AS [DivisionsAlongSlope],
                [surfaces-per-division] AS [SurfacesPerDivision],
                [non-circular-number-of-iterations] AS [NumberOfIterations],
                [non-circular-divisions-next-iteration] AS [DivisionsNextIteration],
                [number-of-vertices-along-surface] AS [NumberOfVerticesAlongSurface],
                [non-circular-number-of-surfaces] AS [NumberOfSurfaces],
                [number-of-nests] AS [NumberOfNests],
                [maximum-iterations] AS [MaximumIterations],
                [initial-number-of-surface-vertices] AS [InitialNumberOfSurfaceVertices],
                [initial-number-of-iterations] AS [InitialNumberOfIterations],
                [maximum-number-of-steps] AS [MaximumNumberOfSteps],
                [number-of-factors-safety-compared] AS [NumberOfFactorsSafetyComparedBeforeStopping],
                [tolerance-for-stopping-criterion] AS [ToleranceForStoppingCriterion],
                [number-of-particles] AS [NumberOfParticles],
                [simulation-circular-calculation-methods].[calculation-method] AS [CalculationMethods],
                [simulation-non-circular-calculation-methods].[calculation-method] AS [CalculationMethods],
                [created-by].[id] AS [Id],
                [created-by].[first-name] AS [FirstName],
                [created-by].[surname] AS [Surname],
                [created-by].[username] AS [Username],
                [simulation-sections].[id] AS [Id],
                [simulation-sections].[section-id] AS [SectionId],
                [simulation-sections].[ignored-instruments] AS [IgnoredInstruments],
                [simulation-sections].[minimum-drained-depth] AS [MinimumDrainedDepth],
                [simulation-sections].[minimum-pseudo-static-depth] AS [MinimumPseudoStaticDepth],
                [simulation-sections].[minimum-undrained-depth] AS [MinimumUndrainedDepth],
                [simulation-sections].[beach-length-statistical-measure] AS [BeachLengthStatisticalMeasure],
                [simulation-sections].[beach-length] AS [BeachLength],
                [sections].[id] AS [Id],
                [sections].[name] AS [Name],
                [sections].[structure-id] AS [StructureId], 
                [simulation-section-instruments].[id] AS [Id],
                [simulation-section-instruments].[quota] AS [Quota],
                [simulation-section-instruments].[dry-type] AS [DryType],
                [simulation-section-instruments].[dry] AS [Dry],    
                [instruments].[id] AS [Id],
                [instruments].[identifier] AS [Identifier],
                [instruments].[type] AS [Type],
                [instruments].[dry-type] AS [DryType],
                [measurements].[id] AS [Id],
                [measurements].[identifier] AS [identifier],
                [simulation-section-results].[id] AS [Id],
                [simulation-section-results].[calculation-method] AS [CalculationMethod],
                [simulation-section-results].[sli-file-type] AS [SliFileType],
                [simulation-section-results].[value] AS [Value],
                [simulation-section-results].[sli-file-name] AS [Name],
                [simulation-section-results].[sli-file-unique-name] AS [UniqueName],
                [simulation-section-results].[sltm-file-name] AS [Name],
                [simulation-section-results].[sltm-file-unique-name] AS [UniqueName],
                [simulation-section-results].[dxf-file-name] AS [Name],
                [simulation-section-results].[dxf-file-unique-name] AS [UniqueName],
                [simulation-section-results].[png-file-name] AS [Name],
                [simulation-section-results].[png-file-unique-name] AS [UniqueName],
                [users].[id] AS [Id],
                [users].[first-name] AS [FirstName],
                [users].[surname] AS [Surname],
                [users].[username] AS [Username],
                [upstream-linimetric-ruler].[id] AS [Id],
                [upstream-linimetric-ruler].[identifier] AS [Identifier],
                [downstream-linimetric-ruler].[id] AS [Id],
                [downstream-linimetric-ruler].[identifier] AS [Identifier],
                [section-reviews].[id] AS [Id],
                [section-reviews].[index] AS [Index],
                [section-reviews].[start-date] AS [StartDate],
                [simulation-events].[id] AS [Id],
                [simulation-events].[event] AS [Event],
                [simulation-events].[created-date] AS [CreatedDate],
                [construction-stages].[id] AS [Id],
                [construction-stages].[stage] AS [Stage],
                [simulation-warnings].[id] AS [Id],
                [simulation-warnings].[message] AS [Message],
                [simulation-warnings].[created-date] AS [CreatedDate],
                [simulation-warnings].[simulation-section-id] AS [SimulationSectionId]
            FROM [simulations]
            LEFT JOIN [users] [created-by] ON [simulations].[created-by] = [created-by].[id]
            LEFT JOIN [simulation-users] ON [simulations].[id] = [simulation-users].[simulation-id]
            LEFT JOIN [users] ON [simulation-users].[user-id] = [users].[id]
            LEFT JOIN [simulation-circular-calculation-methods] ON [simulations].[id] = [simulation-circular-calculation-methods].[simulation-id]
            LEFT JOIN [simulation-non-circular-calculation-methods] ON [simulations].[id] = [simulation-non-circular-calculation-methods].[simulation-id]
            INNER JOIN [simulation-sections] ON [simulations].[id] = [simulation-sections].[simulation-id]
            INNER JOIN [sections] ON [simulation-sections].[section-id] = [sections].[id]
            LEFT JOIN [construction-stages] ON [simulation-sections].[construction-stage-id] = [construction-stages].[id]
            LEFT JOIN [section-reviews] ON [simulation-sections].[section-review-id] = [section-reviews].[id]
            LEFT JOIN [simulation-section-instruments] ON [simulation-sections].[id] = [simulation-section-instruments].[simulation-section-id]
            LEFT JOIN [instruments] ON [simulation-section-instruments].[instrument-id] = [instruments].[id]
            LEFT JOIN [measurements] ON [simulation-section-instruments].[measurement-id] = [measurements].[id]
            LEFT JOIN [simulation-section-results] ON [simulation-sections].[id] = [simulation-section-results].[simulation-section-id]
            LEFT JOIN [instruments] [upstream-linimetric-ruler] ON [simulations].[upstream-linimetric-ruler-id] = [upstream-linimetric-ruler].[id]
            LEFT JOIN [instruments] [downstream-linimetric-ruler] ON [simulations].[downstream-linimetric-ruler-id] = [downstream-linimetric-ruler].[id]
            LEFT JOIN [simulation-events] ON [simulations].[id] = [simulation-events].[simulation-id]
            LEFT JOIN [simulation-warnings] ON [simulation-sections].[id] = [simulation-warnings].[simulation-section-id]
            WHERE [simulations].[id] = @Id";

        public const string GetByIdForDto = @"
            SELECT
                -- Core simulation data
                [simulations].[id] AS [Id],
                [simulations].[name] AS [Name],
                [simulations].[status] AS [Status],
                [simulations].[should-keep] AS [ShouldKeep],
                [simulations].[should-evaluate-drained-condition] AS [ShouldEvaluateDrainedCondition],
                [simulations].[should-evaluate-undrained-condition] AS [ShouldEvaluateUndrainedCondition],
                [simulations].[should-evaluate-pseudo-static-condition] AS [ShouldEvaluatePseudoStaticCondition],
                [simulations].[safety-factor-target] AS [SafetyFactorTarget],
                [simulations].[water-table-configuration] AS [WaterTableConfiguration],
                [simulations].[reading-statistical-measure] AS [ReadingStatisticalMeasure],
                [simulations].[water-table-variation] AS [WaterTableVariation],
                [simulations].[start-date] AS [StartDate],
                [simulations].[end-date] AS [EndDate],
                [simulations].[upstream-linimetric-ruler-statistical-measure] AS [UpstreamLinimetricRulerStatisticalMeasure],
                [simulations].[downstream-linimetric-ruler-statistical-measure] AS [DownstreamLinimetricRulerStatisticalMeasure],
                [simulations].[upstream-linimetric-ruler-quota] AS [UpstreamLinimetricRulerQuota],
                [simulations].[downstream-linimetric-ruler-quota] AS [DownstreamLinimetricRulerQuota],
                [simulations].[ignore-damaged-instruments] AS [IgnoreDamagedInstruments],
                [simulations].[need-to-do-statistical-calculations] AS [NeedToDoStatisticalCalculations],

                -- Seismic coefficient
                [simulations].[seismic-coefficient-horizontal] AS [Horizontal],
                [simulations].[seismic-coefficient-vertical] AS [Vertical],

                -- Circular parameters
                [circular-search-method] AS [CircularSearchMethod],
                [circular-divisions-along-slope] AS [CircularDivisionsAlongSlope],
                [circles-per-division] AS [CirclesPerDivision],
                [circular-number-of-iterations] AS [CircularNumberOfIterations],
                [circular-divisions-next-iteration] AS [CircularDivisionsNextIteration],
                [radius-increment] AS [RadiusIncrement],
                [circular-number-of-surfaces] AS [CircularNumberOfSurfaces],

                -- Non-circular parameters
                [non-circular-search-method] AS [NonCircularSearchMethod],
                [non-circular-divisions-along-slope] AS [NonCircularDivisionsAlongSlope],
                [surfaces-per-division] AS [SurfacesPerDivision],
                [non-circular-number-of-iterations] AS [NonCircularNumberOfIterations],
                [non-circular-divisions-next-iteration] AS [NonCircularDivisionsNextIteration],
                [number-of-vertices-along-surface] AS [NumberOfVerticesAlongSurface],
                [non-circular-number-of-surfaces] AS [NonCircularNumberOfSurfaces],
                [number-of-nests] AS [NumberOfNests],
                [maximum-iterations] AS [MaximumIterations],
                [initial-number-of-surface-vertices] AS [InitialNumberOfSurfaceVertices],
                [initial-number-of-iterations] AS [InitialNumberOfIterations],
                [maximum-number-of-steps] AS [MaximumNumberOfSteps],
                [number-of-factors-safety-compared] AS [NumberOfFactorsSafetyComparedBeforeStopping],
                [tolerance-for-stopping-criterion] AS [ToleranceForStoppingCriterion],
                [number-of-particles] AS [NumberOfParticles],

                -- Calculation methods
                [simulation-circular-calculation-methods].[calculation-method] AS [CircularCalculationMethod],
                [simulation-non-circular-calculation-methods].[calculation-method] AS [NonCircularCalculationMethod],

                -- Created by user
                [created-by].[id] AS [CreatedById],
                [created-by].[first-name] AS [CreatedByFirstName],
                [created-by].[surname] AS [CreatedBySurname],
                [created-by].[username] AS [CreatedByUsername],

                -- Sections
                [simulation-sections].[id] AS [SectionId],
                [simulation-sections].[section-id] AS [SectionSectionId],
                [sections].[name] AS [SectionName],
                [simulation-sections].[ignored-instruments] AS [SectionIgnoredInstruments],
                [simulation-sections].[minimum-drained-depth] AS [SectionMinimumDrainedDepth],
                [simulation-sections].[minimum-pseudo-static-depth] AS [SectionMinimumPseudoStaticDepth],
                [simulation-sections].[minimum-undrained-depth] AS [SectionMinimumUndrainedDepth],
                [simulation-sections].[beach-length-statistical-measure] AS [SectionBeachLengthStatisticalMeasure],
                [simulation-sections].[beach-length] AS [SectionBeachLength],
                [section-reviews].[id] AS [SectionReviewId],
                [section-reviews].[index] AS [SectionReviewIndex],
                [section-reviews].[start-date] AS [SectionReviewStartDate],
                [construction-stages].[id] AS [ConstructionStageId],
                [construction-stages].[stage] AS [ConstructionStage],

                -- Instruments
                [simulation-section-instruments].[id] AS [InstrumentId],
                [simulation-section-instruments].[quota] AS [InstrumentQuota],
                [simulation-section-instruments].[dry-type] AS [InstrumentDryType],
                [simulation-section-instruments].[dry] AS [InstrumentDry],
                [instruments].[id] AS [InstrumentInstrumentId],
                [instruments].[identifier] AS [InstrumentIdentifier],
                [instruments].[type] AS [InstrumentType],
                [measurements].[id] AS [InstrumentMeasurementId],
                [measurements].[identifier] AS [InstrumentMeasurementIdentifier],

                -- Results
                [simulation-section-results].[id] AS [ResultId],
                [simulation-section-results].[calculation-method] AS [ResultCalculationMethod],
                [simulation-section-results].[sli-file-type] AS [ResultSliFileType],
                [simulation-section-results].[value] AS [ResultValue],
                [simulation-section-results].[dxf-file-name] AS [ResultDxfFileName],
                [simulation-section-results].[png-file-name] AS [ResultPngFileName],

                -- Events
                [simulation-events].[id] AS [EventId],
                [simulation-events].[event] AS [Event],
                [simulation-events].[created-date] AS [EventCreatedDate],

                -- Warnings
                [simulation-warnings].[id] AS [WarningId],
                [simulation-warnings].[message] AS [WarningMessage],
                [simulation-warnings].[created-date] AS [WarningCreatedDate],

                -- Linimetric rulers
                [upstream-linimetric-ruler].[id] AS [UpstreamLinimetricRulerId],
                [downstream-linimetric-ruler].[id] AS [DownstreamLinimetricRulerId]

            FROM [simulations]
            LEFT JOIN [users] [created-by] ON [simulations].[created-by] = [created-by].[id]
            LEFT JOIN [simulation-circular-calculation-methods] ON [simulations].[id] = [simulation-circular-calculation-methods].[simulation-id]
            LEFT JOIN [simulation-non-circular-calculation-methods] ON [simulations].[id] = [simulation-non-circular-calculation-methods].[simulation-id]
            INNER JOIN [simulation-sections] ON [simulations].[id] = [simulation-sections].[simulation-id]
            INNER JOIN [sections] ON [simulation-sections].[section-id] = [sections].[id]
            LEFT JOIN [construction-stages] ON [simulation-sections].[construction-stage-id] = [construction-stages].[id]
            LEFT JOIN [section-reviews] ON [simulation-sections].[section-review-id] = [section-reviews].[id]
            LEFT JOIN [simulation-section-instruments] ON [simulation-sections].[id] = [simulation-section-instruments].[simulation-section-id]
            LEFT JOIN [instruments] ON [simulation-section-instruments].[instrument-id] = [instruments].[id]
            LEFT JOIN [measurements] ON [simulation-section-instruments].[measurement-id] = [measurements].[id]
            LEFT JOIN [simulation-section-results] ON [simulation-sections].[id] = [simulation-section-results].[simulation-section-id]
            LEFT JOIN [instruments] [upstream-linimetric-ruler] ON [simulations].[upstream-linimetric-ruler-id] = [upstream-linimetric-ruler].[id]
            LEFT JOIN [instruments] [downstream-linimetric-ruler] ON [simulations].[downstream-linimetric-ruler-id] = [downstream-linimetric-ruler].[id]
            LEFT JOIN [simulation-events] ON [simulations].[id] = [simulation-events].[simulation-id]
            LEFT JOIN [simulation-warnings] ON [simulation-sections].[id] = [simulation-warnings].[simulation-section-id]
            WHERE [simulations].[id] = @Id";

        public const string UpdateNameAndKeepStatus = @"
            UPDATE [simulations]
            SET [name] = @Name,
                [should-keep] = @ShouldKeep
            WHERE [id] = @Id";

        public const string Update = @"
            UPDATE [simulations]
            SET
                [upstream-linimetric-ruler-quota] = @UpstreamLinimetricRulerQuota,
                [downstream-linimetric-ruler-quota] = @DownstreamLinimetricRulerQuota,
                [need-to-do-statistical-calculations] = @NeedToDoStatisticalCalculations,
                [status] = @Status
            WHERE [id] = @Id";

        public const string UpdateSimulationSection = @"
            UPDATE [simulation-sections]
            SET [ignored-instruments] = @IgnoredInstruments,
                [beach-length] = @BeachLength
            WHERE [id] = @Id";

        public const string UpdateSimulationSectionInstrument = @"
            UPDATE [simulation-section-instruments]
            SET [quota] = @Quota
            WHERE [id] = @Id";

        public const string DeleteSimulationSectionInstrument = @"
            DELETE FROM [simulation-section-instruments]
            WHERE [simulation-section-id] IN (
                SELECT [id]
                FROM [simulation-sections]
                WHERE [simulation-id] = @SimulationId
            ) AND [instrument-id] NOT IN @InstrumentIds
        ";

        public const string InsertSimulationResult = @"
            INSERT INTO dbo.[simulation-section-results] 
            (id, 
            [simulation-section-id], 
            [calculation-method], 
            [sli-file-type], 
            value, 
            [sli-file-name], 
            [sli-file-unique-name], 
            [sltm-file-name], 
            [sltm-file-unique-name], 
            [dxf-file-name], 
            [dxf-file-unique-name], 
            [png-file-name], 
            [png-file-unique-name])
            VALUES 
            (@id, 
            @SimulationSectionId, 
            @CalculationMethod,
            @SliFileType, 
            @Value, 
            @SliFileName, 
            @SliFileUniqueName, 
            @SltmFileName, 
            @SltmFileUniqueName, 
            @DxfFileName, 
            @DxfFileUniqueName,
            @PngFileName,
            @PngFileUniqueName)";

        public const string Delete = @"
            DELETE FROM [simulations]
            WHERE [id] = @Id";

        public const string DeleteWarnings = @"
            DELETE [simulation-warnings]
            WHERE [simulation-section-id] IN (
                SELECT [id]
                FROM [simulation-sections]
                WHERE [simulation-id] = @SimulationId
            )";
    }
}
