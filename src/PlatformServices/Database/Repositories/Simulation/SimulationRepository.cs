using Dapper;
using Database.Repositories.Outbox;
using Domain.Entities;
using Domain.Enums;
using Domain.Messages.Commands.Simulation;
using Domain.ValueObjects;
using Model.Simulation.List.Request;
using Model.Simulation.List.Response;
using Model.Simulation.Search.Request;
using Model.Simulation.Search.Response;
using Model.Simulation.UpdateStatus.Request;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Model.Core.Search.Pagination;
using Model.Reading.Search.Response;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;

namespace Database.Repositories.Simulation
{
    public class SimulationRepository : ISimulationRepository
    {
        private readonly string _connectionString;

        public SimulationRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<IEnumerable<ListSimulationResponse>> ListAsync(ListSimulationRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<ListSimulationResponse>(Queries.List, new
            {
                request.DaysOfCreation,
                request.ShouldKeep
            });
        }

        public async Task UpdateStatusAsync(UpdateSimulationStatusRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpdateStatus, new { request.Id, request.Status }, transaction);

                if (!string.IsNullOrEmpty(request.Event))   
                {
                    var param = new
                    {
                        Id = Guid.NewGuid(),
                        request.Event,
                        SimulationId = request.Id
                    };

                    await connection.ExecuteAsync(Queries.InsertSimulationEvent, param, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<PaginationResponse> SearchAsync(SearchSimulationRequest request)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@Skip", request.Query.GetSkip());
            parameters.Add("@PageSize", request.Query.PageSize);
            parameters.Add("@RequestedBy", request.RequestedBy);
            
            var builder = new SqlBuilder()
                .Where("[simulation-users].[user-id] = @RequestedBy");

            if (!string.IsNullOrWhiteSpace(request.Query.Name))
            {
                parameters.Add(
                    "@Name", 
                    request.Query.Name, 
                    DbType.AnsiString, 
                    size: 255);
                
                builder = builder.Where(
                    "[simulations].[name] LIKE CONCAT('%', @Name, '%')");
            }

            if (request.Query.ClientId.HasValue)
            {
                parameters.Add("@ClientId", request.Query.ClientId);
                builder = builder
                    .Where("[client-units].[client-id] = @ClientId");
            }

            if (request.Query.ClientUnitId.HasValue)
            {
                parameters.Add("@ClientUnitId", request.Query.ClientUnitId);
                builder = builder
                    .Where("[client-units].[id] = @ClientUnitId");
            }
            
            if (request.Query.StructureId.HasValue)
            {
                parameters.Add("@StructureId", request.Query.StructureId);
                builder = builder
                    .Where("[structures].[id] = @StructureId");
            }
            
            if (request.Query.SearchIdentifier.HasValue)
            {
                parameters.Add("@SearchIdentifier", request.Query.SearchIdentifier);
                builder = builder
                    .Where("[simulations].[search-identifier] = @SearchIdentifier");
            }
            
            if (request.Body.SectionIds.Any())
            {
                parameters.Add("@SectionIds", request.Body.SectionIds);
                builder = builder
                    .Where("[sections].[id] IN @SectionIds");
            }
            
            if (!request.RequestedBySuperSupport)
            {
                parameters.Add("@RequestedUserStructures", request.RequestedUserStructures);
                builder = builder
                    .Where("[structures].[id] IN @RequestedUserStructures");
            }

            var itemsQuery = builder.AddTemplate(Queries.Search);
            var countQuery = builder.AddTemplate(Queries.Count);

            var lookup = new Dictionary<Guid, SearchSimulationResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: itemsQuery.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Simulation),
                    typeof(Domain.Entities.User),
                    typeof(Domain.Entities.SimulationSection),
                    typeof(Domain.Entities.Section),
                    typeof(Domain.Entities.SimulationResult),
                    typeof(Model._Shared.Structure.Structure)
                },
                (records) =>
                {
                    var simulationDb = records[0] as Domain.Entities.Simulation;
                    var createdBy = records[1] as Domain.Entities.User;
                    var simulationSection = records[2] as SimulationSection;
                    var section = records[3] as Domain.Entities.Section;
                    var structure = records[5] as Model._Shared.Structure.Structure;

                    if (!lookup.TryGetValue(simulationDb.Id, out var simulation))
                    {
                        var simulationModel = new SearchSimulationResponse
                        {
                            Id = simulationDb.Id,
                            Name = simulationDb.Name,
                            Status = simulationDb.Status,
                            ShouldKeep = simulationDb.ShouldKeep,
                            SearchIdentifier = simulationDb.SearchIdentifier,
                            CreatedBy = new()
                            {
                                Id = createdBy.Id,
                                FirstName = createdBy.FirstName,
                                Surname = createdBy.Surname,
                                Username = createdBy.Username
                            },
                            CreatedDate = simulationDb.CreatedDate,
                            NeedToDoStatisticalCalculations = simulationDb.NeedToDoStatisticalCalculations,
                            Sections = new()
                        };

                        lookup.Add(simulationDb.Id, simulationModel);

                        simulation = simulationModel;
                    }

                    if (!simulation.Sections.Any(x => x.SectionId == section.Id))
                    {
                        simulation.Sections.Add(new()
                        {
                            SectionId = section.Id,
                            SectionName = section.Name,
                            StructureId = section.StructureId,
                            Structure = structure,
                            Results = new()
                        });
                    }

                    if (records[4] is SimulationResult simulationResult)
                    {
                        var simulationSectionModel = simulation.Sections.First(x => x.SectionId == section.Id);

                        simulationSectionModel.Results.Add(new()
                        {
                            CalculationMethod = simulationResult.CalculationMethod,
                            SliFileType = simulationResult.SliFileType,
                            Value = simulationResult.Value,
                        });
                    }

                    return simulation;
                },
                splitOn: "Id,Id,Id,Id,Id",
                param: parameters);

            var items = lookup.Values.ToList();
            
            if (!items.Any())
            {
                return new PaginationResponse()
                {
                    Data = Enumerable.Empty<SearchSimulationResponse>(),
                };
            }
            
            var count = await connection.ExecuteScalarAsync<int>(
                countQuery.RawSql,
                parameters);

            return new PaginationResponse()
            {
                Data = items,
                CurrentItemsCount = items.Count(),
                TotalItemsCount = count
            };
        }

        public async Task RemoveAuthorizedUserAsync(Guid simulationId, Guid user)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteAsync(Queries.DeleteSimulationUser, new { SimulationId = simulationId, UserId = user });
        }

        public async Task AddAuthorizedUsersAsync(Guid simulationId, List<Guid> users)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            var param = users.Select(user => new
            {
                SimulationId = simulationId,
                UserId = user
            });

            await connection.ExecuteAsync(Queries.InsertSimulationUsers, param);
        }

        public async Task AddAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Name,
                    simulation.Status,
                    simulation.ShouldEvaluateDrainedCondition,
                    simulation.ShouldEvaluateUndrainedCondition,
                    simulation.ShouldEvaluatePseudoStaticCondition,
                    simulation.SafetyFactorTarget,
                    SeismicCoefficientHorizontal = simulation.SeismicCoefficient?.Horizontal,
                    SeismicCoefficientVertical = simulation.SeismicCoefficient?.Vertical,
                    simulation.Slide2Configuration.CircularParameters?.CircularSearchMethod,
                    CircularDivisionsAlongSlope = simulation.Slide2Configuration.CircularParameters?.DivisionsAlongSlope,
                    simulation.Slide2Configuration.CircularParameters?.CirclesPerDivision,
                    CircularNumberOfIterations = simulation.Slide2Configuration.CircularParameters?.NumberOfIterations,
                    CircularDivisionsNextIteration = simulation.Slide2Configuration.CircularParameters?.DivisionsNextIteration,
                    simulation.Slide2Configuration.CircularParameters?.RadiusIncrement,
                    CircularNumberOfSurfaces = simulation.Slide2Configuration.CircularParameters?.NumberOfSurfaces,
                    simulation.Slide2Configuration.NonCircularParameters?.NonCircularSearchMethod,
                    NonCircularDivisionsAlongSlope = simulation.Slide2Configuration.NonCircularParameters?.DivisionsAlongSlope,
                    simulation.Slide2Configuration.NonCircularParameters?.SurfacesPerDivision,
                    NonCircularNumberOfIterations = simulation.Slide2Configuration.NonCircularParameters?.NumberOfIterations,
                    NonCircularDivisionsNextIteration = simulation.Slide2Configuration.NonCircularParameters?.DivisionsNextIteration,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfVerticesAlongSurface,
                    NonCircularNumberOfSurfaces = simulation.Slide2Configuration.NonCircularParameters?.NumberOfSurfaces,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfNests,
                    simulation.Slide2Configuration.NonCircularParameters?.MaximumIterations,
                    simulation.Slide2Configuration.NonCircularParameters?.InitialNumberOfSurfaceVertices,
                    simulation.Slide2Configuration.NonCircularParameters?.InitialNumberOfIterations,
                    simulation.Slide2Configuration.NonCircularParameters?.MaximumNumberOfSteps,
                    NumberOfFactorsSafetyCompared = simulation.Slide2Configuration.NonCircularParameters?.NumberOfFactorsSafetyComparedBeforeStopping,
                    simulation.Slide2Configuration.NonCircularParameters?.ToleranceForStoppingCriterion,
                    simulation.Slide2Configuration.NonCircularParameters?.NumberOfParticles,
                    simulation.WaterTableConfiguration,
                    simulation.ReadingStatisticalMeasure,
                    simulation.WaterTableVariation,
                    simulation.StartDate,
                    simulation.EndDate,
                    simulation.UpstreamLinimetricRulerStatisticalMeasure,
                    simulation.UpstreamLinimetricRulerQuota,
                    simulation.DownstreamLinimetricRulerStatisticalMeasure,
                    simulation.DownstreamLinimetricRulerQuota,
                    simulation.IgnoreDamagedInstruments,
                    simulation.NeedToDoStatisticalCalculations,
                    CreatedBy = simulation.CreatedBy.Id,
                    UpstreamLinimetricRulerId = simulation.UpstreamLinimetricRuler?.Id,
                    DownstreamLinimetricRulerId = simulation.DownstreamLinimetricRuler?.Id
                };

                await connection
                   .ExecuteAsync(Queries.Insert, param, transaction);

                var simulationUsersParam = simulation.AuthorizedUsers.Select(user => new
                {
                    SimulationId = simulation.Id,
                    UserId = user.Id
                });

                await connection
                    .ExecuteAsync(Queries.InsertSimulationUsers, simulationUsersParam, transaction);

                if (simulation.Slide2Configuration.CircularParameters?.CalculationMethods != null)
                {
                    var circularCalculationMethodsParam = simulation.Slide2Configuration.CircularParameters.CalculationMethods.Select(method => new
                    {
                        SimulationId = simulation.Id,
                        CalculationMethod = method
                    });

                    await connection
                        .ExecuteAsync(Queries.InsertCircularCalculationMethod, circularCalculationMethodsParam, transaction);
                }

                if (simulation.Slide2Configuration.NonCircularParameters?.CalculationMethods != null)
                {
                    var nonCircularCalculationMethodsParam = simulation.Slide2Configuration.NonCircularParameters.CalculationMethods.Select(method => new
                    {
                        SimulationId = simulation.Id,
                        CalculationMethod = method
                    });

                    await connection
                        .ExecuteAsync(Queries.InsertNonCircularCalculationMethod, nonCircularCalculationMethodsParam, transaction);
                }

                var simulationSectionParam = simulation.Sections.Select(simulationSection => new
                {
                    simulationSection.Id,
                    SectionReviewId = simulationSection.SectionReview.Id,
                    SimulationId = simulation.Id,
                    SectionId = simulationSection.Section.Id,
                    ConstructionStageId = simulationSection.ConstructionStage?.Id,
                    simulationSection.MinimumDrainedDepth,
                    simulationSection.MinimumUndrainedDepth,
                    simulationSection.MinimumPseudoStaticDepth,
                    simulationSection.BeachLengthStatisticalMeasure,
                    simulationSection.BeachLength,
                });

                await connection
                    .ExecuteAsync(Queries.InsertSimulationSection, simulationSectionParam, transaction);

                var simulationSectionInstruments = simulation.Sections.SelectMany(section => section.Instruments.Select(simulationSectionInstrument => new
                {
                    simulationSectionInstrument.Id,
                    SimulationSectionId = section.Id,
                    InstrumentId = simulationSectionInstrument.Instrument.Id,
                    MeasurementId = simulationSectionInstrument.Measurement?.Id,
                    simulationSectionInstrument.Quota,
                    simulationSectionInstrument.DryType,
                    simulationSectionInstrument.Dry
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationSectionInstrument, simulationSectionInstruments, transaction);

                foreach (var @event in simulation.Events)
                {
                    var eventParam = new
                    {
                        @event.Id,
                        @event.Event,
                        SimulationId = simulation.Id,
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertSimulationEvent, eventParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                await connection.ExecuteAsync(Queries.DeleteWarnings, new { SimulationId = id }, transaction);
                await connection.ExecuteAsync(Queries.Delete, new { Id = id }, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<GetSimulationByIdResponse> GetByIdAsync(GetSimulationByIdRequest request)
        {
            var lookup = new Dictionary<Guid, GetSimulationByIdResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetByIdForDto,
                param: new { Id = request.Id },
                types: new[]
                {
                    typeof(SimulationDtoMapping),
                    typeof(SeismicCoefficientDtoMapping),
                    typeof(CircularParametersDtoMapping),
                    typeof(NonCircularParametersDtoMapping),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                    typeof(CreatedByDtoMapping),
                    typeof(SectionDtoMapping),
                    typeof(InstrumentDtoMapping),
                    typeof(ResultDtoMapping),
                    typeof(EventDtoMapping),
                    typeof(WarningDtoMapping),
                    typeof(LinimetricRulerDtoMapping)
                },
                splitOn: "Horizontal,CircularSearchMethod,NonCircularSearchMethod,CircularCalculationMethod,NonCircularCalculationMethod,CreatedById,SectionId,InstrumentId,ResultId,EventId,WarningId,UpstreamLinimetricRulerId",
                map: (records) =>
                {
                    var simulation = records[0] as SimulationDtoMapping;
                    var seismicCoeff = records[1] as SeismicCoefficientDtoMapping;
                    var circularParams = records[2] as CircularParametersDtoMapping;
                    var nonCircularParams = records[3] as NonCircularParametersDtoMapping;
                    var circularCalculationMethod = (CalculationMethod?)records[4];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[5];
                    var createdBy = records[6] as CreatedByDtoMapping;
                    var section = records[7] as SectionDtoMapping;
                    var instrument = records[8] as InstrumentDtoMapping;
                    var result = records[9] as ResultDtoMapping;
                    var eventData = records[10] as EventDtoMapping;
                    var warning = records[11] as WarningDtoMapping;
                    var linimetricRulers = records[12] as LinimetricRulerDtoMapping;

                    if (!lookup.TryGetValue(simulation.Id, out var simulationResponse))
                    {
                        simulationResponse = new GetSimulationByIdResponse
                        {
                            Id = simulation.Id,
                            Name = simulation.Name,
                            Status = simulation.Status,
                            ShouldKeep = simulation.ShouldKeep,
                            ShouldEvaluateDrainedCondition = simulation.ShouldEvaluateDrainedCondition,
                            ShouldEvaluateUndrainedCondition = simulation.ShouldEvaluateUndrainedCondition,
                            ShouldEvaluatePseudoStaticCondition = simulation.ShouldEvaluatePseudoStaticCondition,
                            SafetyFactorTarget = simulation.SafetyFactorTarget,
                            WaterTableConfiguration = simulation.WaterTableConfiguration,
                            ReadingStatisticalMeasure = simulation.ReadingStatisticalMeasure,
                            WaterTableVariation = simulation.WaterTableVariation,
                            StartDate = simulation.StartDate,
                            EndDate = simulation.EndDate,
                            UpstreamLinimetricRulerStatisticalMeasure = simulation.UpstreamLinimetricRulerStatisticalMeasure,
                            UpstreamLinimetricRulerId = linimetricRulers?.UpstreamLinimetricRulerId,
                            UpstreamLinimetricRulerQuota = simulation.UpstreamLinimetricRulerQuota,
                            DownstreamLinimetricRulerStatisticalMeasure = simulation.DownstreamLinimetricRulerStatisticalMeasure,
                            DownstreamLinimetricRulerId = linimetricRulers?.DownstreamLinimetricRulerId,
                            DownstreamLinimetricRulerQuota = simulation.DownstreamLinimetricRulerQuota,
                            IgnoreDamagedInstruments = simulation.IgnoreDamagedInstruments,
                            NeedToDoStatisticalCalculations = simulation.NeedToDoStatisticalCalculations,
                            CreatedBy = createdBy != null ? new Model._Shared.User.User
                            {
                                Id = createdBy.CreatedById,
                                FirstName = createdBy.CreatedByFirstName,
                                Surname = createdBy.CreatedBySurname,
                                Username = createdBy.CreatedByUsername
                            } : null,
                            ZipFileDownloadUrl = string.Format(Model.Constants.EndpointUrl.SimulationsZipFile, simulation.Id),
                            Sections = new List<GetSimulationByIdSection>(),
                            Events = new List<GetSimulationByIdEvent>()
                        };

                        lookup.Add(simulation.Id, simulationResponse);
                    }

                    // Handle seismic coefficient
                    if (seismicCoeff != null && (seismicCoeff.Horizontal.HasValue || seismicCoeff.Vertical.HasValue))
                    {
                        simulationResponse = simulationResponse with
                        {
                            SeismicCoefficient = new Model._Shared.Orientation.Orientation
                            {
                                Horizontal = seismicCoeff.Horizontal ?? 0,
                                Vertical = seismicCoeff.Vertical ?? 0
                            }
                        };
                        lookup[simulation.Id] = simulationResponse;
                    }

                    // Handle slide2 configuration
                    if (circularParams != null || nonCircularParams != null)
                    {
                        var slide2Config = new Model._Shared.Slide2Configuration.Slide2Configuration();

                        if (circularParams != null)
                        {
                            slide2Config = slide2Config with
                            {
                                CircularParameters = new Model._Shared.Slide2Configuration.CircularParameters
                                {
                                    CircularSearchMethod = circularParams.CircularSearchMethod ?? Domain.Enums.CircularSearchMethod.GridSearch,
                                    DivisionsAlongSlope = circularParams.CircularDivisionsAlongSlope,
                                    CirclesPerDivision = circularParams.CirclesPerDivision,
                                    NumberOfIterations = circularParams.CircularNumberOfIterations,
                                    DivisionsNextIteration = circularParams.CircularDivisionsNextIteration,
                                    RadiusIncrement = (int?)(circularParams.RadiusIncrement),
                                    NumberOfSurfaces = circularParams.CircularNumberOfSurfaces,
                                    CalculationMethods = new List<CalculationMethod>()
                                }
                            };
                        }

                        if (nonCircularParams != null)
                        {
                            slide2Config = slide2Config with
                            {
                                NonCircularParameters = new Model._Shared.Slide2Configuration.NonCircularParameters
                                {
                                    NonCircularSearchMethod = nonCircularParams.NonCircularSearchMethod ?? Domain.Enums.NonCircularSearchMethod.BlockSearch,
                                    DivisionsAlongSlope = nonCircularParams.NonCircularDivisionsAlongSlope,
                                    SurfacesPerDivision = nonCircularParams.SurfacesPerDivision,
                                    NumberOfIterations = nonCircularParams.NonCircularNumberOfIterations,
                                    DivisionsNextIteration = nonCircularParams.NonCircularDivisionsNextIteration,
                                    NumberOfVerticesAlongSurface = nonCircularParams.NumberOfVerticesAlongSurface,
                                    NumberOfSurfaces = nonCircularParams.NonCircularNumberOfSurfaces,
                                    NumberOfNests = nonCircularParams.NumberOfNests,
                                    MaximumIterations = nonCircularParams.MaximumIterations,
                                    InitialNumberOfSurfaceVertices = nonCircularParams.InitialNumberOfSurfaceVertices,
                                    InitialNumberOfIterations = nonCircularParams.InitialNumberOfIterations,
                                    MaximumNumberOfSteps = nonCircularParams.MaximumNumberOfSteps,
                                    NumberOfFactorsSafetyComparedBeforeStopping = nonCircularParams.NumberOfFactorsSafetyComparedBeforeStopping,
                                    ToleranceForStoppingCriterion = nonCircularParams.ToleranceForStoppingCriterion,
                                    NumberOfParticles = nonCircularParams.NumberOfParticles,
                                    CalculationMethods = new List<CalculationMethod>()
                                }
                            };
                        }

                        simulationResponse = simulationResponse with { Slide2Configuration = slide2Config };
                        lookup[simulation.Id] = simulationResponse;
                    }

                    // Handle calculation methods
                    if (circularCalculationMethod != null && simulationResponse.Slide2Configuration?.CircularParameters != null)
                    {
                        var circularParamsConfig = simulationResponse.Slide2Configuration.CircularParameters;
                        if (!circularParamsConfig.CalculationMethods.Contains(circularCalculationMethod.Value))
                        {
                            circularParamsConfig.CalculationMethods.Add(circularCalculationMethod.Value);
                        }
                    }

                    if (nonCircularCalculationMethod != null && simulationResponse.Slide2Configuration?.NonCircularParameters != null)
                    {
                        var nonCircularParamsConfig = simulationResponse.Slide2Configuration.NonCircularParameters;
                        if (!nonCircularParamsConfig.CalculationMethods.Contains(nonCircularCalculationMethod.Value))
                        {
                            nonCircularParamsConfig.CalculationMethods.Add(nonCircularCalculationMethod.Value);
                        }
                    }

                    // Handle sections
                    if (section != null)
                    {
                        var existingSection = simulationResponse.Sections.FirstOrDefault(s => s.Id == section.SectionId);
                        if (existingSection == null)
                        {
                            var newSection = new GetSimulationByIdSection
                            {
                                Id = section.SectionId,
                                SectionId = section.SectionSectionId,
                                SectionName = section.SectionName,
                                SectionReviewId = section.SectionReviewId,
                                SectionReviewIndex = section.SectionReviewIndex,
                                SectionReviewStartDate = section.SectionReviewStartDate,
                                ConstructionStageId = section.ConstructionStageId,
                                ConstructionStage = section.ConstructionStage,
                                MinimumDrainedDepth = section.SectionMinimumDrainedDepth,
                                MinimumUndrainedDepth = section.SectionMinimumUndrainedDepth,
                                MinimumPseudoStaticDepth = section.SectionMinimumPseudoStaticDepth,
                                BeachLengthStatisticalMeasure = section.SectionBeachLengthStatisticalMeasure,
                                BeachLength = section.SectionBeachLength,
                                IgnoredInstruments = section.SectionIgnoredInstruments,
                                Instruments = new List<GetSimulationByIdInstrument>(),
                                Results = new List<GetSimulationByIdResult>(),
                                Warnings = new List<GetSimulationByIdWarning>()
                            };

                            simulationResponse.Sections.Add(newSection);
                            existingSection = newSection;
                        }

                        // Handle instruments
                        if (instrument != null)
                        {
                            var existingInstrument = existingSection.Instruments.FirstOrDefault(i => i.Id == instrument.InstrumentId);
                            if (existingInstrument == null)
                            {
                                existingSection.Instruments.Add(new GetSimulationByIdInstrument
                                {
                                    Id = instrument.InstrumentId,
                                    InstrumentId = instrument.InstrumentInstrumentId,
                                    InstrumentIdentifier = instrument.InstrumentIdentifier,
                                    InstrumentType = instrument.InstrumentType,
                                    InstrumentDryType = instrument.InstrumentDryType,
                                    MeasurementId = instrument.InstrumentMeasurementId,
                                    MeasurementIdentifier = instrument.InstrumentMeasurementIdentifier,
                                    Quota = instrument.InstrumentQuota,
                                    Dry = instrument.InstrumentDry
                                });
                            }
                        }

                        // Handle results
                        if (result != null)
                        {
                            var existingResult = existingSection.Results.FirstOrDefault(r => r.Id == result.ResultId);
                            if (existingResult == null)
                            {
                                existingSection.Results.Add(new GetSimulationByIdResult
                                {
                                    Id = result.ResultId,
                                    CalculationMethod = result.ResultCalculationMethod,
                                    SliFileType = result.ResultSliFileType,
                                    Value = result.ResultValue,
                                    DxfFile = !string.IsNullOrEmpty(result.ResultDxfFileName) ? new Model._Shared.File.File { Name = result.ResultDxfFileName } : null,
                                    PngFile = !string.IsNullOrEmpty(result.ResultPngFileName) ? new Model._Shared.File.File { Name = result.ResultPngFileName } : null,
                                    ZipFileDownloadUrl = string.Format(Model.Constants.EndpointUrl.SimulationsResultZipFile, simulation.Id, result.ResultId)
                                });
                            }
                        }

                        // Handle warnings
                        if (warning != null)
                        {
                            var existingWarning = existingSection.Warnings.FirstOrDefault(w => w.Id == warning.WarningId);
                            if (existingWarning == null)
                            {
                                existingSection.Warnings.Add(new GetSimulationByIdWarning
                                {
                                    Id = warning.WarningId,
                                    Message = warning.WarningMessage,
                                    CreatedDate = warning.WarningCreatedDate
                                });
                            }
                        }
                    }

                    // Handle events
                    if (eventData != null)
                    {
                        var existingEvent = simulationResponse.Events.FirstOrDefault(e => e.Id == eventData.EventId);
                        if (existingEvent == null)
                        {
                            simulationResponse.Events.Add(new GetSimulationByIdEvent
                            {
                                Id = eventData.EventId,
                                Event = eventData.Event,
                                CreatedDate = eventData.EventCreatedDate
                            });
                        }
                    }

                    return simulationResponse;
                });

            // Order events by created date descending
            var result = lookup.Values.FirstOrDefault();
            if (result != null)
            {
                result = result with
                {
                    Events = result.Events.OrderByDescending(e => e.CreatedDate).ToList()
                };
            }

            return result;
        }

        public async Task<Domain.Entities.Simulation> GetAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Simulation>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetById,
                param: new { Id = id },
                types: new[]
                {
                    typeof(Domain.Entities.Simulation),
                    typeof(Orientation),
                    typeof(CircularParameters),
                    typeof(NonCircularParameters),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                    typeof(Domain.Entities.User),
                    typeof(SimulationSection),
                    typeof(Domain.Entities.Section),
                    typeof(SimulationInstrument),
                    typeof(Domain.Entities.Instrument),
                    typeof(Measurement),
                    typeof(SimulationResult),
                    typeof(File),
                    typeof(File),
                    typeof(File),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Instrument),
                    typeof(SectionReview),
                    typeof(SimulationEvent),
                    typeof(ConstructionStage),
                    typeof(SimulationWarning),
                },
                splitOn: "Horizontal,CircularSearchMethod,NonCircularSearchMethod,CalculationMethods,CalculationMethods,Id,Id,Id,Id,Id,Id,Id,Name,Name,Name,Name,Id,Id,Id,Id,Id,Id,Id",
                map: (records) =>
                {
                    var simulation = records[0] as Domain.Entities.Simulation;
                    var circularParameters = records[2] as CircularParameters;
                    var nonCircularParameters = records[3] as NonCircularParameters;
                    var circularCalculationMethod = (CalculationMethod?)records[4];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[5];
                    var createdBy = records[6] as Domain.Entities.User;
                    var section = records[8] as Domain.Entities.Section;
                    var instrument = records[10] as Domain.Entities.Instrument;
                    var measurement = records[11] as Measurement;
                    var sliFile = records[13] as File;
                    var sltmFile = records[14] as File;
                    var dxfFile = records[15] as File;
                    var pngFile = records[16] as File;
                    var upstreamLinimetricRuler = records[18] as Domain.Entities.Instrument;
                    var downstreamLinimetricRuler = records[19] as Domain.Entities.Instrument;
                    var sectionReview = records[20] as SectionReview;

                    if (!lookup.TryGetValue(simulation.Id, out var simulationModel))
                    {
                        simulationModel = simulation;
                        simulationModel.CreatedBy = new()
                        {
                            Id = createdBy.Id,
                            FirstName = createdBy.FirstName,
                            Surname = createdBy.Surname,
                            Username = createdBy.Username
                        };
                        simulationModel.Slide2Configuration.CircularParameters = circularParameters;
                        simulationModel.Slide2Configuration.NonCircularParameters = nonCircularParameters;
                        simulationModel.UpstreamLinimetricRuler = upstreamLinimetricRuler;
                        simulationModel.DownstreamLinimetricRuler = downstreamLinimetricRuler;

                        lookup.Add(simulation.Id, simulationModel);
                    }

                    if (records[1] is Orientation orientation)
                    {
                        simulationModel.SeismicCoefficient = new()
                        {
                            Horizontal = orientation.Horizontal,
                            Vertical = orientation.Vertical
                        };
                    }

                    if (circularCalculationMethod != null && simulationModel.Slide2Configuration.CircularParameters != null && !simulationModel.Slide2Configuration.CircularParameters.CalculationMethods.Any(x => x == circularCalculationMethod))
                    {
                        simulationModel.Slide2Configuration.CircularParameters.CalculationMethods.Add(circularCalculationMethod.Value);
                    }

                    if (nonCircularCalculationMethod != null && simulationModel.Slide2Configuration.NonCircularParameters != null && !simulationModel.Slide2Configuration.NonCircularParameters.CalculationMethods.Any(x => x == nonCircularCalculationMethod))
                    {
                        simulationModel.Slide2Configuration.NonCircularParameters.CalculationMethods.Add(nonCircularCalculationMethod.Value);
                    }

                    if (records[7] is SimulationSection simulationSection)
                    {
                        var sectionModel = new SimulationSection();

                        if (!simulationModel.Sections.Any(x => x.Id == simulationSection.Id))
                        {
                            sectionModel = new SimulationSection
                            {
                                Id = simulationSection.Id,
                                Section = section,
                                SectionReview = sectionReview,
                                MinimumDrainedDepth = simulationSection.MinimumDrainedDepth,
                                MinimumUndrainedDepth = simulationSection.MinimumUndrainedDepth,
                                MinimumPseudoStaticDepth = simulationSection.MinimumPseudoStaticDepth,
                                BeachLengthStatisticalMeasure = simulationSection.BeachLengthStatisticalMeasure,
                                IgnoredInstruments = simulationSection.IgnoredInstruments,
                                BeachLength = simulationSection.BeachLength,
                                Instruments = new()
                            };

                            if (records[22] is ConstructionStage constructionStage)
                            {
                                sectionModel.ConstructionStage = constructionStage; 
                            }

                            simulationModel.Sections.Add(sectionModel);
                        }
                        else
                        {
                            sectionModel = simulationModel.Sections.First(x => x.Id == simulationSection.Id);
                        }

                        if (records[23] is SimulationWarning simulationWarning) 
                        {
                            var warningSection = simulationModel.Sections.FirstOrDefault(x => x.Id == simulationSection.Id);

                            if (warningSection != null && !warningSection.Warnings.Any(x => x.Id == simulationWarning.Id))
                            {
                                warningSection.Warnings.Add(new()
                                {
                                    Id = simulationWarning.Id,
                                    Message = simulationWarning.Message,
                                    CreatedDate = simulationWarning.CreatedDate
                                });
                            }
                        }   

                        if (records[9] is SimulationInstrument simulationInstrument && !sectionModel.Instruments.Any(x => x.Id == simulationInstrument.Id))
                        {
                            sectionModel.Instruments.Add(new()
                            {
                                Id = simulationInstrument.Id,
                                Instrument = instrument,
                                Measurement = measurement,
                                Quota = simulationInstrument.Quota,
                                DryType = simulationInstrument.DryType,
                                Dry = simulationInstrument.Dry
                            });
                        }

                        if (records[12] is SimulationResult simulationResult && !sectionModel.Results.Any(x => x.Id == simulationResult.Id))
                        {
                            sectionModel.Results.Add(new()
                            {
                                Id = simulationResult.Id,
                                CalculationMethod = simulationResult.CalculationMethod,
                                SliFileType = simulationResult.SliFileType,
                                Value = simulationResult.Value,
                                DxfFile = dxfFile,
                                PngFile = pngFile,
                                SliFile = sliFile,
                                SltmFile = sltmFile
                            });
                        }
                    }

                    if (records[17] is Domain.Entities.User user && !simulationModel.AuthorizedUsers.Any(x => x.Id == user.Id))
                    {
                        simulationModel.AuthorizedUsers.Add(new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        });
                    }

                    if (records[21] is SimulationEvent @event && !simulationModel.Events.Any(x => x.Id == @event.Id))
                    {
                        simulationModel.Events.Add(@event);
                    }

                    return simulationModel;
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpdateAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Name,
                    simulation.ShouldKeep
                };  

                await connection
                    .ExecuteAsync(Queries.UpdateNameAndKeepStatus, param, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception) 
            {
                await transaction.RollbackAsync();
                throw;
            }

        }

        public async Task PatchAsync(Domain.Entities.Simulation simulation)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    simulation.Id,
                    simulation.Status,
                    simulation.UpstreamLinimetricRulerQuota,
                    simulation.DownstreamLinimetricRulerQuota,
                    simulation.NeedToDoStatisticalCalculations
                };

                await connection
                    .ExecuteAsync(Queries.Update, param, transaction);

                var simulationSectionParam = simulation.Sections.Select(simulationSection => new
                {
                    simulationSection.Id,
                    simulationSection.BeachLength,
                    simulationSection.IgnoredInstruments
                });

                await connection
                    .ExecuteAsync(Queries.UpdateSimulationSection, simulationSectionParam, transaction);

                var simulationSectionInstruments = simulation.Sections.SelectMany(section => section.Instruments.Select(simulationSectionInstrument => new
                {
                    simulationSectionInstrument.Id,
                    simulationSectionInstrument.Quota
                }));

                await connection
                    .ExecuteAsync(Queries.UpdateSimulationSectionInstrument, simulationSectionInstruments, transaction);

                await connection
                    .ExecuteAsync(Queries.DeleteSimulationSectionInstrument, new { SimulationId = simulation.Id, InstrumentIds = simulation.Sections.SelectMany(x => x.Instruments).Select(x => x.Instrument.Id) }, transaction);

                var results = simulation.Sections.SelectMany(section => section.Results.Select(result => new
                {
                    result.Id,
                    SimulationSectionId = section.Id,
                    result.CalculationMethod,
                    result.SliFileType,
                    result.Value,
                    SliFileName = result.SliFile.Name,
                    SliFileUniqueName = result.SliFile.UniqueName,
                    SltmFileName = result.SltmFile.Name,
                    SltmFileUniqueName = result.SltmFile.UniqueName,
                    DxfFileName = result.DxfFile.Name,
                    DxfFileUniqueName = result.DxfFile.UniqueName,
                    PngFileName = result.PngFile.Name,
                    PngFileUniqueName = result.PngFile.UniqueName
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationResult, results, transaction);

                foreach (var @event in simulation.Events)
                {
                    var eventParam = new
                    {
                        @event.Id,
                        @event.Event,
                        SimulationId = simulation.Id
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertSimulationEvent, eventParam, transaction);
                }

                var warnings = simulation.Sections.SelectMany(section => section.Warnings.Select(warning => new
                {
                    warning.Id,
                    SimulationSectionId = section.Id,
                    warning.Message
                }));

                await connection
                    .ExecuteAsync(Queries.InsertSimulationWarning, warnings, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }

    // Internal DTO mapping classes for optimized Dapper mapping
    internal class SimulationDtoMapping
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Domain.Enums.SimulationStatus Status { get; set; }
        public bool ShouldKeep { get; set; }
        public bool ShouldEvaluateDrainedCondition { get; set; }
        public bool ShouldEvaluateUndrainedCondition { get; set; }
        public bool ShouldEvaluatePseudoStaticCondition { get; set; }
        public double? SafetyFactorTarget { get; set; }
        public Domain.Enums.WaterTableConfiguration WaterTableConfiguration { get; set; }
        public Domain.Enums.ReadingStatisticalMeasure? ReadingStatisticalMeasure { get; set; }
        public double? WaterTableVariation { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Domain.Enums.ReadingStatisticalMeasure? UpstreamLinimetricRulerStatisticalMeasure { get; set; }
        public Domain.Enums.ReadingStatisticalMeasure? DownstreamLinimetricRulerStatisticalMeasure { get; set; }
        public double? UpstreamLinimetricRulerQuota { get; set; }
        public double? DownstreamLinimetricRulerQuota { get; set; }
        public bool IgnoreDamagedInstruments { get; set; }
        public bool NeedToDoStatisticalCalculations { get; set; }
    }

    internal class SeismicCoefficientDtoMapping
    {
        public double? Horizontal { get; set; }
        public double? Vertical { get; set; }
    }

    internal class CircularParametersDtoMapping
    {
        public Domain.Enums.CircularSearchMethod? CircularSearchMethod { get; set; }
        public int? CircularDivisionsAlongSlope { get; set; }
        public int? CirclesPerDivision { get; set; }
        public int? CircularNumberOfIterations { get; set; }
        public int? CircularDivisionsNextIteration { get; set; }
        public double? RadiusIncrement { get; set; }
        public int? CircularNumberOfSurfaces { get; set; }
    }

    internal class NonCircularParametersDtoMapping
    {
        public Domain.Enums.NonCircularSearchMethod? NonCircularSearchMethod { get; set; }
        public int? NonCircularDivisionsAlongSlope { get; set; }
        public int? SurfacesPerDivision { get; set; }
        public int? NonCircularNumberOfIterations { get; set; }
        public int? NonCircularDivisionsNextIteration { get; set; }
        public int? NumberOfVerticesAlongSurface { get; set; }
        public int? NonCircularNumberOfSurfaces { get; set; }
        public int? NumberOfNests { get; set; }
        public int? MaximumIterations { get; set; }
        public int? InitialNumberOfSurfaceVertices { get; set; }
        public int? InitialNumberOfIterations { get; set; }
        public int? MaximumNumberOfSteps { get; set; }
        public int? NumberOfFactorsSafetyComparedBeforeStopping { get; set; }
        public double? ToleranceForStoppingCriterion { get; set; }
        public int? NumberOfParticles { get; set; }
    }

    internal class CreatedByDtoMapping
    {
        public Guid CreatedById { get; set; }
        public string CreatedByFirstName { get; set; }
        public string CreatedBySurname { get; set; }
        public string CreatedByUsername { get; set; }
    }

    internal class SectionDtoMapping
    {
        public Guid SectionId { get; set; }
        public Guid SectionSectionId { get; set; }
        public string SectionName { get; set; }
        public string SectionIgnoredInstruments { get; set; }
        public double? SectionMinimumDrainedDepth { get; set; }
        public double? SectionMinimumPseudoStaticDepth { get; set; }
        public double? SectionMinimumUndrainedDepth { get; set; }
        public Domain.Enums.ReadingStatisticalMeasure? SectionBeachLengthStatisticalMeasure { get; set; }
        public double? SectionBeachLength { get; set; }
        public Guid? SectionReviewId { get; set; }
        public int? SectionReviewIndex { get; set; }
        public DateTime? SectionReviewStartDate { get; set; }
        public Guid? ConstructionStageId { get; set; }
        public string ConstructionStage { get; set; }
    }

    internal class InstrumentDtoMapping
    {
        public Guid InstrumentId { get; set; }
        public decimal? InstrumentQuota { get; set; }
        public Domain.Enums.DryType? InstrumentDryType { get; set; }
        public bool InstrumentDry { get; set; }
        public Guid InstrumentInstrumentId { get; set; }
        public string InstrumentIdentifier { get; set; }
        public Domain.Enums.InstrumentType InstrumentType { get; set; }
        public Guid? InstrumentMeasurementId { get; set; }
        public string InstrumentMeasurementIdentifier { get; set; }
    }

    internal class ResultDtoMapping
    {
        public Guid ResultId { get; set; }
        public Domain.Enums.CalculationMethod ResultCalculationMethod { get; set; }
        public Domain.Enums.SliFileType ResultSliFileType { get; set; }
        public double ResultValue { get; set; }
        public string ResultDxfFileName { get; set; }
        public string ResultPngFileName { get; set; }
    }

    internal class EventDtoMapping
    {
        public Guid EventId { get; set; }
        public string Event { get; set; }
        public DateTime EventCreatedDate { get; set; }
    }

    internal class WarningDtoMapping
    {
        public Guid WarningId { get; set; }
        public string WarningMessage { get; set; }
        public DateTime WarningCreatedDate { get; set; }
    }

    internal class LinimetricRulerDtoMapping
    {
        public Guid? UpstreamLinimetricRulerId { get; set; }
        public Guid? DownstreamLinimetricRulerId { get; set; }
    }
}
