using Application.Core;
using Application.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Simulation;
using Microsoft.Extensions.Options;
using Model.Simulation.GetById.Request;
using Model.Simulation.GetById.Response;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Simulation.GetById.Response.GetSimulationByIdResponse>;

namespace Application.Simulation.GetById
{
    public sealed class GetSimulationByIdUseCase : IGetSimulationByIdUseCase
    {
        private readonly ISimulationRepository _simulationRepository;
        private readonly IBlobStorageService _blobService;
        private readonly BlobStorageOptions _blobOptions;
        private readonly GetSimulationByIdRequestValidator _requestValidator = new();

        public GetSimulationByIdUseCase(
            ISimulationRepository simulationRepository,
            IBlobStorageService blobService,
            IOptions<BlobStorageOptions> blobOptions)
        {
            _simulationRepository = simulationRepository;
            _blobService = blobService;
            _blobOptions = blobOptions.Value;
        }

        public async Task<UseCaseResponse<GetSimulationByIdResponse>> Execute(GetSimulationByIdRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be null.");
                }

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var result = await _simulationRepository
                    .GetByIdAsync(request);

                if (result is null)
                {
                    return NoContent();
                }

                // TODO: baixar os arquivos
                
                return Ok(result);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        public async Task<GetSimulationByIdResponse> Map(Domain.Entities.Simulation simulation)
        {
            var response = new GetSimulationByIdResponse()
            {
                Id = simulation.Id,
                Name = simulation.Name,
                Status = simulation.Status,
                ShouldKeep = simulation.ShouldKeep,
                UpstreamLinimetricRulerId = simulation.UpstreamLinimetricRuler?.Id,
                DownstreamLinimetricRulerId = simulation.DownstreamLinimetricRuler?.Id,
                CreatedBy = new()
                {
                    Id = simulation.CreatedBy.Id,
                    FirstName = simulation.CreatedBy.FirstName,
                    Surname = simulation.CreatedBy.Surname,
                    Username = simulation.CreatedBy.Username
                },
                DownstreamLinimetricRulerQuota = simulation.DownstreamLinimetricRulerQuota,
                DownstreamLinimetricRulerStatisticalMeasure = simulation.DownstreamLinimetricRulerStatisticalMeasure,
                UpstreamLinimetricRulerQuota = simulation.UpstreamLinimetricRulerQuota,
                UpstreamLinimetricRulerStatisticalMeasure = simulation.UpstreamLinimetricRulerStatisticalMeasure,
                StartDate = simulation.StartDate,
                EndDate = simulation.EndDate,
                WaterTableVariation = simulation.WaterTableVariation,
                IgnoreDamagedInstruments = simulation.IgnoreDamagedInstruments,
                NeedToDoStatisticalCalculations = simulation.NeedToDoStatisticalCalculations,
                ReadingStatisticalMeasure = simulation.ReadingStatisticalMeasure,
                SafetyFactorTarget = simulation.SafetyFactorTarget,
                SeismicCoefficient = simulation.SeismicCoefficient != null ? new Model._Shared.Orientation.Orientation()
                {
                    Horizontal = simulation.SeismicCoefficient.Horizontal,
                    Vertical = simulation.SeismicCoefficient.Vertical
                } : null,
                ShouldEvaluateDrainedCondition = simulation.ShouldEvaluateDrainedCondition,
                ShouldEvaluatePseudoStaticCondition = simulation.ShouldEvaluatePseudoStaticCondition,
                ShouldEvaluateUndrainedCondition = simulation.ShouldEvaluateUndrainedCondition,
                WaterTableConfiguration = simulation.WaterTableConfiguration,
                Slide2Configuration = new()
                {
                    CircularParameters = simulation.Slide2Configuration.CircularParameters != null
                    ? new()
                    {
                        CalculationMethods = simulation.Slide2Configuration.CircularParameters.CalculationMethods,
                        CirclesPerDivision = simulation.Slide2Configuration.CircularParameters.CirclesPerDivision,
                        CircularSearchMethod = simulation.Slide2Configuration.CircularParameters.CircularSearchMethod,
                        DivisionsAlongSlope = simulation.Slide2Configuration.CircularParameters.DivisionsAlongSlope,
                        DivisionsNextIteration = simulation.Slide2Configuration.CircularParameters.DivisionsNextIteration,
                        NumberOfIterations = simulation.Slide2Configuration.CircularParameters.NumberOfIterations,
                        NumberOfSurfaces = simulation.Slide2Configuration.CircularParameters.NumberOfSurfaces,
                        RadiusIncrement = simulation.Slide2Configuration.CircularParameters.RadiusIncrement
                    }
                    : null,
                    NonCircularParameters = simulation.Slide2Configuration.NonCircularParameters != null
                    ? new()
                    {
                        CalculationMethods = simulation.Slide2Configuration.NonCircularParameters.CalculationMethods,
                        DivisionsAlongSlope = simulation.Slide2Configuration.NonCircularParameters.DivisionsAlongSlope,
                        DivisionsNextIteration = simulation.Slide2Configuration.NonCircularParameters.DivisionsNextIteration,
                        NumberOfIterations = simulation.Slide2Configuration.NonCircularParameters.NumberOfIterations,
                        NumberOfSurfaces = simulation.Slide2Configuration.NonCircularParameters.NumberOfSurfaces,
                        InitialNumberOfIterations = simulation.Slide2Configuration.NonCircularParameters.InitialNumberOfIterations,
                        InitialNumberOfSurfaceVertices = simulation.Slide2Configuration.NonCircularParameters.InitialNumberOfSurfaceVertices,
                        MaximumIterations = simulation.Slide2Configuration.NonCircularParameters.MaximumIterations,
                        MaximumNumberOfSteps = simulation.Slide2Configuration.NonCircularParameters.MaximumNumberOfSteps,
                        NonCircularSearchMethod = simulation.Slide2Configuration.NonCircularParameters.NonCircularSearchMethod,
                        NumberOfFactorsSafetyComparedBeforeStopping = simulation.Slide2Configuration.NonCircularParameters.NumberOfFactorsSafetyComparedBeforeStopping,
                        NumberOfNests = simulation.Slide2Configuration.NonCircularParameters.NumberOfNests,
                        NumberOfParticles = simulation.Slide2Configuration.NonCircularParameters.NumberOfParticles,
                        NumberOfVerticesAlongSurface = simulation.Slide2Configuration.NonCircularParameters.NumberOfVerticesAlongSurface,
                        SurfacesPerDivision = simulation.Slide2Configuration.NonCircularParameters.SurfacesPerDivision,
                        ToleranceForStoppingCriterion = simulation.Slide2Configuration.NonCircularParameters.ToleranceForStoppingCriterion
                    }
                    : null
                },
                ZipFileDownloadUrl = string.Format(Model.Constants.EndpointUrl.SimulationsZipFile, simulation.Id),
                Events = simulation.Events.Select(e => new GetSimulationByIdEvent()
                {
                    Id = e.Id,
                    Event = e.Event,
                    CreatedDate = e.CreatedDate
                }).OrderByDescending(x => x.CreatedDate).ToList(),
                Sections = new()
            };

            foreach (var section in simulation.Sections)
            {
                var sectionModel = new GetSimulationByIdSection()
                {
                    Id = section.Id,
                    SectionId = section.Section.Id,
                    SectionName = section.Section.Name,
                    SectionReviewId = section.SectionReview?.Id,
                    SectionReviewIndex = section.SectionReview?.Index,
                    SectionReviewStartDate = section.SectionReview?.StartDate,
                    ConstructionStageId = section.ConstructionStage?.Id,
                    ConstructionStage = section.ConstructionStage?.Stage,
                    MinimumDrainedDepth = section.MinimumDrainedDepth,
                    MinimumPseudoStaticDepth = section.MinimumPseudoStaticDepth,
                    MinimumUndrainedDepth = section.MinimumUndrainedDepth,
                    BeachLengthStatisticalMeasure = section.BeachLengthStatisticalMeasure,
                    BeachLength = section.BeachLength,
                    IgnoredInstruments = section.IgnoredInstruments,
                    Results = new(),
                    Warnings = section.Warnings.Select(w => new GetSimulationByIdWarning()
                    {
                        Id = w.Id,
                        Message = w.Message,
                        CreatedDate = w.CreatedDate
                    }).ToList(),
                    Instruments = section.Instruments.Select(i => new GetSimulationByIdInstrument()
                    {
                        Id = i.Id,
                        Dry = i.Dry,
                        InstrumentId = i.Instrument.Id,
                        InstrumentIdentifier = i.Instrument.Identifier,
                        MeasurementId = i.Measurement?.Id,
                        MeasurementIdentifier = i.Measurement?.Identifier,
                        InstrumentType = i.Instrument.Type,
                        InstrumentDryType = i.Instrument.DryType,
                        Quota = i.Quota
                    }).ToList(),
                };

                foreach (var result in section.Results)
                {
                    var pngFile = new Model._Shared.File.File()
                    {
                        Name = result.PngFile.Name,
                        Base64 = Convert.ToBase64String(await _blobService.GetAsync(result.PngFile.UniqueName, _blobOptions.ClientsContainer))
                    };

                    var dxfFile = new Model._Shared.File.File()
                    {
                        Name = result.DxfFile.Name,
                        Base64 = Convert.ToBase64String(await _blobService.GetAsync(result.DxfFile.UniqueName, _blobOptions.ClientsContainer))
                    };

                    sectionModel.Results.Add(new GetSimulationByIdResult()
                    {
                        Id = result.Id,
                        CalculationMethod = result.CalculationMethod,
                        Value = result.Value,
                        SliFileType = result.SliFileType,
                        ZipFileDownloadUrl = string.Format(Model.Constants.EndpointUrl.SimulationsResultZipFile, simulation.Id, result.Id),
                        PngFile = pngFile,
                        DxfFile = dxfFile
                    });
                }

                response.Sections.Add(sectionModel);
            }

            return response;
        }
    }
}
